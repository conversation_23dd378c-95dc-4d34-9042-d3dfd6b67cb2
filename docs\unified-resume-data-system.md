# 统一简历数据管理系统

## 📋 项目概述

本项目实现了一个统一的简历数据管理系统，解决了原有系统中数据分散、传递复杂、缺乏统一管理的问题。

## 🎯 解决的问题

### 原有问题
1. **数据存储分散**：每个模块都有独立的存储键（如 `basicInfo`、`education`、`workList` 等）
2. **数据传递复杂**：页面间通过 URL 参数传递大量 JSON 数据
3. **缺乏统一管理**：虽然有 `resumeManager.js`，但实际使用中仍然大量直接操作 `wx.getStorageSync/setStorageSync`
4. **数据一致性问题**：同一份数据可能存在多个地方，容易出现不同步

### 解决方案
1. **统一数据类**：基于 `resume.py` 创建 JavaScript 版本的简历数据类
2. **全局管理器**：实现全局单例管理器，统一处理数据的读写和状态管理
3. **数据验证**：内置数据验证和默认值处理
4. **兼容性迁移**：支持从旧的分散存储自动迁移数据

## 🏗️ 系统架构

### 核心组件

#### 1. ResumeData 类 (`utils/resume/ResumeData.js`)
- **功能**：定义统一的简历数据结构
- **特性**：
  - 基于 `resume.py` 的 JavaScript 实现
  - 包含所有简历模块的数据类
  - 内置数据验证和序列化方法
  - 支持克隆和转换操作

#### 2. GlobalResumeManager 类 (`utils/resume/GlobalResumeManager.js`)
- **功能**：全局简历数据管理器
- **特性**：
  - 单例模式，全局唯一实例
  - 自动数据迁移（从旧存储结构）
  - 数据变更监听机制
  - 兼容性存储（同时保存到新旧结构）

#### 3. 测试系统 (`pages/test/resumeDataTest/`)
- **功能**：验证数据管理系统的正确性
- **测试内容**：
  - 数据创建和验证
  - 序列化和反序列化
  - 管理器操作
  - 数据迁移

## 📁 文件结构

```
utils/resume/
├── ResumeData.js              # 简历数据类定义
├── GlobalResumeManager.js     # 全局管理器
└── resumeManager.js           # 原有管理器（保留兼容）

pages/test/resumeDataTest/     # 测试页面
├── resumeDataTest.js
├── resumeDataTest.wxml
├── resumeDataTest.wxss
└── resumeDataTest.json

pages/makeResume/
├── makeResumeNew.js           # 新版简历制作页面（演示）
├── makeResumeNew.wxml
├── makeResumeNew.wxss
└── makeResumeNew.json

docs/
└── unified-resume-data-system.md  # 本文档
```

## 🚀 使用方法

### 1. 在应用启动时初始化

```javascript
// app.js
const globalResumeManager = require('./utils/resume/GlobalResumeManager');

App({
  async onLaunch() {
    // 初始化全局简历管理器
    await this.initResumeManager();
  },

  async initResumeManager() {
    try {
      await globalResumeManager.initialize();
      console.log('全局简历管理器初始化完成');
    } catch (error) {
      console.error('全局简历管理器初始化失败:', error);
    }
  },

  getResumeManager() {
    return globalResumeManager;
  }
});
```

### 2. 在页面中使用

```javascript
// 页面 JS
const app = getApp();

Page({
  async onLoad() {
    // 获取全局管理器
    const resumeManager = app.getResumeManager();
    
    // 获取当前简历
    const currentResume = resumeManager.getCurrentResume();
    
    // 更新字段
    resumeManager.updateField('basicInfo.name', '张三');
    
    // 保存数据
    await resumeManager.saveCurrentResume();
    
    // 添加监听器
    resumeManager.addListener('pageKey', (action, data) => {
      console.log('数据变更:', action, data);
    });
  },

  onUnload() {
    // 移除监听器
    const resumeManager = app.getResumeManager();
    resumeManager.removeListener('pageKey');
  }
});
```

### 3. 数据验证

```javascript
const { ResumeData } = require('../../utils/resume/ResumeData.js');

// 创建简历实例
const resume = new ResumeData({
  basicInfo: {
    name: '张三',
    phone: '13800138000'
  }
});

// 验证数据
const errors = resume.validate();
if (errors.length > 0) {
  console.log('验证错误:', errors);
}

// 转换为对象
const obj = resume.toObject();

// 转换为JSON
const json = resume.toJSON();
```

## 🔄 数据迁移

系统会自动从旧的分散存储迁移数据：

### 旧存储结构
```
wx.getStorageSync('basicInfo')
wx.getStorageSync('education')
wx.getStorageSync('workList')
...
```

### 新存储结构
```
wx.getStorageSync('currentResumeId')
wx.getStorageSync('resume_[id]')
```

### 迁移过程
1. 检查是否存在新结构数据
2. 如果不存在，从旧结构读取数据
3. 创建新的 ResumeData 实例
4. 保存到新结构
5. 保持旧结构兼容性

## 📊 测试验证

### 运行测试
1. 在微信开发者工具中访问测试页面：`/pages/test/resumeDataTest/resumeDataTest`
2. 查看测试结果，验证各项功能

### 测试内容
- ✅ 数据创建：测试 ResumeData 类的实例化
- ✅ 数据验证：测试数据完整性验证
- ✅ 数据序列化：测试 JSON 转换和恢复
- ✅ 管理器操作：测试全局管理器功能
- ✅ 数据迁移：测试从旧存储结构迁移

## 🎨 新版页面演示

新版简历制作页面 (`/pages/makeResume/makeResumeNew`) 演示了如何使用统一数据管理：

### 特性
- 实时数据同步
- 统一状态管理
- 数据变更监听
- 自动保存机制

### 对比优势
| 功能 | 旧版本 | 新版本 |
|------|--------|--------|
| 数据获取 | 分散的 `wx.getStorageSync` | 统一的 `resumeManager.getCurrentResume()` |
| 数据保存 | 手动保存到多个键 | 自动保存到统一结构 |
| 数据传递 | URL 参数传递 JSON | 全局实例共享 |
| 状态同步 | 手动刷新 | 自动监听变更 |

## 🔧 配置选项

### 全局管理器配置
```javascript
// 添加监听器
resumeManager.addListener(key, callback);

// 移除监听器
resumeManager.removeListener(key);

// 更新字段
resumeManager.updateField(fieldPath, value);

// 创建新简历
resumeManager.createNewResume(title);
```

### 数据类配置
```javascript
// 验证数据
const errors = resumeData.validate();

// 克隆实例
const cloned = resumeData.clone();

// 更新时间戳
resumeData.touch();
```

## 🚧 后续计划

### 阶段二：重构现有页面
- [ ] 修改各个编辑页面使用新的数据管理
- [ ] 更新数据保存逻辑
- [ ] 统一数据传递方式

### 阶段三：性能优化
- [ ] 实现数据缓存机制
- [ ] 优化大数据量处理
- [ ] 添加数据压缩

### 阶段四：扩展功能
- [ ] 多简历管理
- [ ] 数据导入导出
- [ ] 云端同步

## 📝 注意事项

1. **兼容性**：新系统保持与旧系统的兼容性，可以逐步迁移
2. **性能**：全局管理器使用单例模式，避免重复初始化
3. **内存**：注意及时移除不需要的监听器，避免内存泄漏
4. **错误处理**：所有异步操作都有错误处理机制

## 🤝 贡献指南

1. 在修改数据结构时，确保更新对应的验证逻辑
2. 添加新功能时，编写相应的测试用例
3. 保持代码注释的完整性和准确性
4. 遵循现有的代码风格和命名规范
