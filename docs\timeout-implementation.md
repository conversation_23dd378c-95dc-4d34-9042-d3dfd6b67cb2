# 简历预览和PDF下载超时功能实现

## 功能概述

为简历预览图片生成和PDF下载功能添加了统一的超时机制，超时时间设置为10秒。当服务器在指定时间内没有返回响应时，自动结束请求、释放加载动画、解除页面阻塞。

## 实现方案

### 1. 环境配置

**文件**: `config/apiConfig.js`

在API配置文件中添加了全局超时配置：

```javascript
const apiConfig = {
    // 全局超时配置
    timeout: {
        // 简历预览图片生成超时时间（毫秒）
        previewImage: 10000, // 10秒
        // PDF生成超时时间（毫秒）
        generatePDF: 10000,  // 10秒
        // 普通API请求超时时间（毫秒）
        default: 5000        // 5秒
    },
    // ... 其他配置
}
```

**优势**：
- 集中管理超时配置
- 不同类型请求可设置不同超时时间
- 便于环境配置和调试

### 2. 请求层超时实现

**文件**: `utils/api/request.js`

在底层请求工具中实现了通用的超时机制：

#### 核心实现逻辑：

```javascript
// 设置超时定时器
let timeoutTimer = null;
let isCompleted = false;

if (timeoutMs > 0) {
  timeoutTimer = setTimeout(() => {
    if (!isCompleted) {
      isCompleted = true;
      
      // 释放加载动画
      if (showLoading) {
        wx.hideLoading();
      }

      // 创建超时错误
      const timeoutError = new Error('请求超时，请检查网络连接');
      timeoutError.isTimeout = true;
      timeoutError.isNetworkError = true;
      
      // 显示超时提示
      if (showError) {
        wx.showToast({
          title: '请求超时，请重试',
          icon: 'none',
          duration: 3000
        });
      }
      
      reject(timeoutError);
    }
  }, timeoutMs);
}
```

#### 关键特性：

1. **状态管理**：使用 `isCompleted` 标志防止重复处理
2. **资源清理**：自动清除定时器和加载状态
3. **错误标识**：为超时错误添加特殊标识 `isTimeout`
4. **用户反馈**：显示明确的超时提示信息

### 3. API层超时配置

**文件**: `utils/api/resumeApi.js`

为具体的API调用添加超时配置：

#### 预览图片API：
```javascript
return request.request({
  // ... 其他配置
  timeout: apiConfig.timeout?.previewImage || 10000 // 10秒超时
});
```

#### PDF生成API：
```javascript
return request.request({
  // ... 其他配置
  timeout: apiConfig.timeout?.generatePDF || 10000 // 10秒超时
});
```

### 4. 业务层超时处理

#### 预览组件处理

**文件**: `pages/makeCreateResume/components/resumePreview/index.js`

```javascript
} catch (error) {
  console.error('预览图片请求失败:', error);
  
  // 检查是否为超时错误
  let errorMessage = '预览图片生成失败';
  if (error.isTimeout) {
    errorMessage = '预览图片生成超时，请重试';
    console.log('预览图片请求超时，已自动释放加载状态');
  }
  
  this.setData({
    imageLoading: false,
    imageError: true,
    requestInProgress: false
  });

  // 显示超时特定的错误提示
  if (error.isTimeout) {
    wx.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });
  }
}
```

#### PDF生成页面处理

**文件**: `pages/makeCreateResume/makeCreateResume.js`

```javascript
} catch (error) {
  wx.hideLoading();
  console.error('生成PDF过程中发生错误:', error);

  // 检查是否为超时错误
  let errorMessage = error.message || '生成PDF失败';
  if (error.isTimeout) {
    errorMessage = 'PDF生成超时，请重试';
    console.log('PDF生成请求超时，已自动释放加载状态');
  }

  wx.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 3000
  });
}
```

## 功能特性

### 1. 自动资源释放

- **加载动画**：超时时自动隐藏loading状态
- **页面阻塞**：解除页面交互阻塞
- **内存清理**：清除定时器，防止内存泄漏

### 2. 用户友好提示

- **明确的错误信息**：区分超时错误和其他错误
- **操作指导**：提示用户可以重试
- **状态反馈**：清晰的视觉状态切换

### 3. 开发者友好

- **详细日志**：记录超时事件和相关信息
- **错误标识**：通过 `error.isTimeout` 标识超时错误
- **配置灵活**：可根据需要调整超时时间

### 4. 性能优化

- **防止重复处理**：避免超时和正常响应的竞态条件
- **资源清理**：及时清理定时器和状态
- **错误恢复**：支持用户重试操作

## 配置说明

### 超时时间配置

```javascript
timeout: {
  previewImage: 10000, // 预览图片：10秒
  generatePDF: 10000,  // PDF生成：10秒
  default: 5000        // 普通请求：5秒
}
```

### 调整建议

1. **网络环境较差**：可适当增加超时时间
2. **服务器性能**：根据服务器处理能力调整
3. **用户体验**：平衡等待时间和用户耐心

## 测试验证

### 测试场景

1. **正常情况**：
   - 服务器正常响应，验证功能不受影响
   - 确认超时机制不会误触发

2. **超时情况**：
   - 模拟网络延迟，验证超时触发
   - 确认加载状态正确释放
   - 验证错误提示正确显示

3. **边界情况**：
   - 在超时临界点的请求处理
   - 快速连续请求的超时处理
   - 页面切换时的超时处理

### 验证要点

- ✅ 超时时自动隐藏loading
- ✅ 显示明确的超时错误提示
- ✅ 页面交互恢复正常
- ✅ 支持用户重试操作
- ✅ 不影响正常的成功响应

## 相关文件

- `config/apiConfig.js` - 超时配置
- `utils/api/request.js` - 底层超时实现
- `utils/api/resumeApi.js` - API层超时配置
- `pages/makeCreateResume/components/resumePreview/index.js` - 预览组件超时处理
- `pages/makeCreateResume/makeCreateResume.js` - PDF生成超时处理

## 总结

通过在多个层次实现超时机制，确保了：

1. **用户体验**：避免无限等待，提供明确反馈
2. **系统稳定**：防止资源泄漏，保持页面响应
3. **开发维护**：统一的超时处理，便于调试和维护
4. **配置灵活**：可根据实际需求调整超时参数

该实现方案既保证了功能的完整性，又提供了良好的用户体验和开发体验。
