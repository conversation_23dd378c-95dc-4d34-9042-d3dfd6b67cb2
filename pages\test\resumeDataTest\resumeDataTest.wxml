<!--简历数据测试页面-->
<view class="test-page">
  <view class="header">
    <text class="title">简历数据管理系统测试</text>
    <view class="status">
      <text class="status-text {{isManagerInitialized ? 'success' : 'error'}}">
        管理器状态: {{isManagerInitialized ? '已初始化' : '未初始化'}}
      </text>
    </view>
  </view>

  <view class="actions">
    <button class="action-btn primary" bindtap="rerunTests">重新运行测试</button>
    <button class="action-btn secondary" bindtap="viewCurrentResume">查看当前简历</button>
    <button class="action-btn secondary" bindtap="clearResults">清空结果</button>
  </view>

  <view class="results-container">
    <view class="results-header">
      <text class="results-title">测试结果 ({{testResults.length}})</text>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view wx:for="{{testResults}}" wx:key="index" class="result-item">
        <view class="result-header">
          <text class="result-time">{{item.time}}</text>
        </view>
        <view class="result-message">{{item.message}}</view>
        <view wx:if="{{item.data}}" class="result-data">
          <text class="data-label">数据:</text>
          <text class="data-content">{{item.data}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <view class="info-panel">
    <text class="info-title">测试说明</text>
    <view class="info-content">
      <text class="info-item">• 数据创建: 测试ResumeData类的实例化</text>
      <text class="info-item">• 数据验证: 测试数据完整性验证</text>
      <text class="info-item">• 数据序列化: 测试JSON转换和恢复</text>
      <text class="info-item">• 管理器操作: 测试全局管理器功能</text>
      <text class="info-item">• 数据迁移: 测试从旧存储结构迁移</text>
    </view>
  </view>
</view>
