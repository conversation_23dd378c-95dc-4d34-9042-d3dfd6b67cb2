<!-- components/customNavBar/customNavBar.wxml -->
<view class="custom-nav-bar" style="height: {{navBarHeight}}px; background-color: {{backgroundColor}};">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 标题栏 -->
  <view class="title-bar" style="height: {{titleBarHeight}}px; color: {{textColor}};">
    <!-- 左侧返回按钮 -->
    <view class="nav-left">
      <view wx:if="{{showBack}}" class="nav-btn back-btn" bindtap="onBackTap">
        <text class="nav-icon">‹</text>
      </view>
    </view>
    
    <!-- 中间标题 -->
    <view class="nav-center">
      <text class="nav-title">{{title}}</text>
    </view>
    
    <!-- 右侧按钮组 -->
    <view class="nav-right">
      <!-- 分享按钮 -->
      <view wx:if="{{showShare}}" class="nav-btn share-btn" bindtap="onShareTap">
        <text class="nav-icon">⤴</text>
      </view>
      
      <!-- 反馈按钮 -->
      <view wx:if="{{showFeedback}}" class="nav-btn feedback-btn" bindtap="onFeedbackTap">
        <text class="nav-icon">💬</text>
      </view>
    </view>
  </view>
</view>
