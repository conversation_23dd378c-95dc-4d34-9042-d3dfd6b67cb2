/* pages/feedback/feedback.wxss */
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-top: 120rpx; /* 为自定义导航栏留出空间 */
}

.section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.required {
  color: #ff4d4f;
  font-size: 24rpx;
}

.optional {
  color: #999;
  font-size: 24rpx;
  font-weight: normal;
}

/* 反馈类型样式 */
.type-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  min-width: 140rpx;
  transition: all 0.3s;
}

.type-item.active {
  border-color: #4B8BF5;
  background-color: #f6f9ff;
}

.type-icon {
  font-size: 40rpx;
  margin-bottom: 10rpx;
}

.type-label {
  font-size: 26rpx;
  color: #666;
}

.type-item.active .type-label {
  color: #4B8BF5;
  font-weight: bold;
}

/* 反馈内容样式 */
.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  line-height: 1.6;
  box-sizing: border-box;
}

.feedback-textarea:focus {
  border-color: #4B8BF5;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 联系方式样式 */
.contact-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.contact-input:focus {
  border-color: #4B8BF5;
}

/* 提交按钮样式 */
.submit-section {
  margin: 40rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin: 0;
}

.submit-btn.enabled {
  background-color: #4B8BF5;
  color: #fff;
}

.submit-btn.disabled {
  background-color: #e8e8e8;
  color: #999;
}

.submit-btn::after {
  border: none;
}

/* 说明文字样式 */
.tips {
  padding: 30rpx 20rpx;
}

.tips-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 2;
  margin-bottom: 10rpx;
}
