/**
 * 性能优化测试脚本
 * 用于验证优化效果
 */

// 模拟微信小程序环境
const mockWx = {
  nextTick: (callback) => setTimeout(callback, 0),
  env: {
    USER_DATA_PATH: '/mock/path'
  },
  getFileSystemManager: () => ({
    writeFileSync: () => {},
    readdir: () => {},
    unlink: () => {}
  })
};

// 设置全局wx对象
global.wx = mockWx;

// 引入性能监控
const performanceMonitor = require('../utils/performance/performanceMonitor');

// 测试数据
const mockResumeData = {
  basicInfo: {
    name: '张三',
    phone: '13800138000',
    email: '<EMAIL>',
    city: '北京市',
    gender: '男',
    age: '25',
    birthday: '1998-01-01',
    marriage: '未婚',
    politics: '群众',
    nation: '汉族',
    hometown: '北京市',
    height: '175cm',
    weight: '70kg',
    photoUrl: 'data:image/jpeg;base64,' + 'x'.repeat(1000) // 模拟头像数据
  },
  jobIntention: {
    position: '前端工程师',
    salary: '15-20k',
    city: '北京市',
    status: '在职-考虑机会'
  },
  education: Array(3).fill({
    school: '北京大学',
    major: '计算机科学与技术',
    degree: '本科',
    startDate: '2016-09',
    endDate: '2020-06',
    description: '主修课程包括数据结构、算法设计、操作系统、计算机网络等。在校期间积极参与各类编程竞赛，获得多项奖励。'
  }),
  work: Array(2).fill({
    company: 'ABC科技有限公司',
    position: '前端开发工程师',
    startDate: '2020-07',
    endDate: '2023-12',
    description: '负责公司主要产品的前端开发工作，包括用户界面设计、交互逻辑实现、性能优化等。熟练使用React、Vue等前端框架，具有丰富的项目经验。'
  }),
  project: Array(4).fill({
    projectName: '企业管理系统',
    role: '前端负责人',
    startDate: '2022-01',
    endDate: '2022-12',
    description: '负责企业管理系统的前端架构设计和开发工作。系统包含用户管理、权限控制、数据统计等多个模块，支持多端适配。'
  }),
  skills: ['JavaScript', 'TypeScript', 'React', 'Vue', 'Node.js', 'Python', 'MySQL', 'MongoDB'],
  awards: ['优秀员工奖', '技术创新奖', '项目贡献奖'],
  interests: ['编程', '阅读', '运动', '旅行'],
  evaluation: [{
    content: '本人性格开朗，工作认真负责，具有良好的团队合作精神。在技术方面，热爱学习新技术，具有较强的学习能力和解决问题的能力。'
  }],
  moduleOrders: {
    basicInfo: 1,
    jobIntention: 2,
    education: 3,
    work: 4,
    project: 5,
    skills: 6,
    awards: 7,
    interests: 8,
    evaluation: 9
  }
};

const mockConfig = {
  themeColor: '#2B6CB0',
  fontSize: 11,
  spacing: 1.2
};

// 测试函数
function testPerformanceOptimization() {
  console.log('========== 性能优化测试开始 ==========');
  
  // 重置监控数据
  performanceMonitor.reset();
  
  // 测试1: 数据大小计算
  console.log('\n1. 测试数据大小计算:');
  const dataSize = performanceMonitor.calculateDataSize(mockResumeData);
  console.log(`简历数据大小: ${performanceMonitor.formatSize(dataSize)}`);
  
  // 测试2: setData调用监控
  console.log('\n2. 测试setData调用监控:');
  
  // 模拟多次setData调用
  performanceMonitor.recordSetData(mockResumeData, 'resumePreview-full');
  performanceMonitor.recordSetData(mockConfig, 'resumePreview-config');
  performanceMonitor.recordSetData(mockResumeData.basicInfo, 'resumePreview-basic');
  performanceMonitor.recordSetData(mockResumeData.education, 'resumePreview-education');
  
  // 测试3: API请求监控
  console.log('\n3. 测试API请求监控:');
  
  // 模拟缓存命中
  const cacheStart = Date.now();
  setTimeout(() => {
    performanceMonitor.recordRequest(cacheStart, Date.now(), true);
  }, 50);
  
  // 模拟网络请求
  const networkStart = Date.now();
  setTimeout(() => {
    performanceMonitor.recordRequest(networkStart, Date.now(), false);
  }, 500);
  
  // 模拟另一个网络请求
  const networkStart2 = Date.now();
  setTimeout(() => {
    performanceMonitor.recordRequest(networkStart2, Date.now(), false);
    
    // 测试4: 性能报告生成
    console.log('\n4. 性能报告:');
    performanceMonitor.printReport();
    
    // 测试5: 数据传输趋势
    console.log('\n5. 数据传输趋势:');
    console.log('趋势:', performanceMonitor.getDataTransferTrend());
    
    console.log('\n========== 性能优化测试完成 ==========');
  }, 500);
}

// 测试数据哈希生成
function testDataHashing() {
  console.log('\n========== 数据哈希测试 ==========');
  
  // 模拟哈希生成函数
  function generateDataHash(resumeData, config, templateId) {
    const dataString = JSON.stringify({
      resumeData: {
        basicInfo: resumeData.basicInfo || {},
        jobIntention: resumeData.jobIntention || {},
        education: resumeData.education || [],
        work: resumeData.work || [],
        project: resumeData.project || [],
        skills: resumeData.skills || [],
        moduleOrders: resumeData.moduleOrders || {}
      },
      config: {
        themeColor: config.themeColor,
        fontSize: config.fontSize,
        spacing: config.spacing
      },
      templateId
    });
    
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }
  
  // 测试相同数据生成相同哈希
  const hash1 = generateDataHash(mockResumeData, mockConfig, 'templateA01');
  const hash2 = generateDataHash(mockResumeData, mockConfig, 'templateA01');
  console.log('相同数据哈希一致性:', hash1 === hash2 ? '✓ 通过' : '✗ 失败');
  
  // 测试不同数据生成不同哈希
  const modifiedConfig = { ...mockConfig, themeColor: '#FF0000' };
  const hash3 = generateDataHash(mockResumeData, modifiedConfig, 'templateA01');
  console.log('不同数据哈希差异性:', hash1 !== hash3 ? '✓ 通过' : '✗ 失败');
  
  console.log('数据哈希值:', hash1);
  console.log('修改后哈希值:', hash3);
}

// 测试缓存机制
function testCacheMechanism() {
  console.log('\n========== 缓存机制测试 ==========');
  
  // 模拟缓存
  const cache = new Map();
  const cacheConfig = {
    maxCacheSize: 3,
    cacheExpireTime: 5000 // 5秒过期
  };
  
  // 添加缓存
  function addToCache(key, value) {
    cache.set(key, {
      url: value,
      timestamp: Date.now()
    });
    console.log(`缓存已添加: ${key}`);
  }
  
  // 检查缓存
  function checkCache(key) {
    const cached = cache.get(key);
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > cacheConfig.cacheExpireTime) {
      cache.delete(key);
      console.log(`缓存已过期: ${key}`);
      return null;
    }
    
    console.log(`缓存命中: ${key}`);
    return cached;
  }
  
  // 测试缓存添加和命中
  addToCache('hash1', 'image1.jpg');
  addToCache('hash2', 'image2.jpg');
  addToCache('hash3', 'image3.jpg');
  
  console.log('缓存命中测试:', checkCache('hash1') ? '✓ 通过' : '✗ 失败');
  console.log('缓存未命中测试:', checkCache('hash4') ? '✗ 失败' : '✓ 通过');
  
  // 测试缓存过期
  setTimeout(() => {
    console.log('5秒后缓存过期测试:', checkCache('hash1') ? '✗ 失败' : '✓ 通过');
  }, 5100);
}

// 运行所有测试
function runAllTests() {
  testDataHashing();
  testCacheMechanism();
  testPerformanceOptimization();
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testPerformanceOptimization,
  testDataHashing,
  testCacheMechanism,
  runAllTests
};
