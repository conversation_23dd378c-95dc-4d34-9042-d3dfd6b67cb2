<view class="container">
  <view class="formGroup">
    <!-- 项目时间 -->
    <view class="formItem">
      <text class="label">项目时间</text>
      <view class="datePicker">
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{projectEditFormData.startDate}}" data-field="startDate" bindchange="handleDateChange">
            <view class="picker {{projectEditFormData.startDate ? '' : 'placeholder'}}">
              {{projectEditFormData.startDate || '请选择'}}
            </view>
          </picker>
        </view>
        <text class="separator">至</text>
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{projectEditFormData.endDate}}" data-field="endDate" bindchange="handleDateChange">
            <view class="picker {{projectEditFormData.endDate ? '' : 'placeholder'}}">
              {{projectEditFormData.endDate || '请选择'}}
            </view>
          </picker>
        </view>
        <view class="nowBtn" bindtap="setEndDateToNow">至今</view>
      </view>
    </view>

    <!-- 项目名称 -->
    <view class="formItem">
      <text class="label">项目名称</text>
      <input class="input" placeholder="请输入" value="{{projectEditFormData.projectName}}" data-field="projectName" bindinput="handleInput"/>
    </view>

    <!-- 担任角色 -->
    <view class="formItem">
      <text class="label">担任角色</text>
      <input class="input" placeholder="请输入 最多15字符" maxlength="15" value="{{projectEditFormData.role}}" data-field="role" bindinput="handleInput"/>
    </view>

    <!-- 项目描述 -->
    <view class="formItem">
      <text class="label">项目描述</text>
      <textarea class="textarea" placeholder="请输入项目描述" value="{{projectEditFormData.description}}" data-field="description" bindinput="handleInput"/>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveInfo">保存</button>
    <button wx:if="{{isEdit}}" class="deleteBtn" bindtap="deleteInfo">删除</button>
  </view>
</view> 