/* 简化管理器测试页面样式 */

.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 15rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 160rpx;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn.secondary {
  background-color: white;
  color: #666;
  border: 2rpx solid #d9d9d9;
}

.action-btn.info {
  background-color: #722ed1;
  color: white;
}

/* 面板通用样式 */
.stats-panel,
.resumes-panel,
.current-resume-panel,
.results-panel {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.panel-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-btn {
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

/* 统计信息 */
.stats-content {
  padding: 20rpx 30rpx 30rpx;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.stat-value.success {
  color: #52c41a;
}

.stat-value.error {
  color: #ff4d4f;
}

/* 简历列表 */
.resumes-list {
  padding: 20rpx;
}

.resume-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  margin-bottom: 15rpx;
  border-radius: 12rpx;
  background-color: #f6f8fa;
  border: 2rpx solid #e1e4e8;
}

.resume-item.current {
  background-color: #e8f5e8;
  border-color: #52c41a;
}

.resume-item:last-child {
  margin-bottom: 0;
}

.resume-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.resume-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.resume-time {
  font-size: 24rpx;
  color: #666;
}

.current-badge {
  background-color: #52c41a;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 22rpx;
}

/* 当前简历信息 */
.resume-content {
  padding: 20rpx 30rpx 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 测试结果 */
.results-list {
  max-height: 600rpx;
  padding: 20rpx;
}

.result-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  border-left: 6rpx solid #1890ff;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  margin-bottom: 10rpx;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-message {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
}
