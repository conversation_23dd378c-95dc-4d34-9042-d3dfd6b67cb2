/**
 * 简化管理器测试页面
 */

const app = getApp();

Page({
  data: {
    testResults: [],
    currentResumeData: null,
    allResumes: [],
    managerStats: null
  },

  onLoad() {
    console.log('=== 简化管理器测试页面加载 ===');
    this.runTests();
  },

  /**
   * 运行所有测试
   */
  async runTests() {
    this.addTestResult('开始测试简化管理器...');
    
    await this.testManagerInitialization();
    await this.testCurrentResumeOperations();
    await this.testFieldUpdates();
    await this.testMultipleResumes();
    await this.testDataPersistence();
    
    this.addTestResult('✅ 所有测试完成');
    this.refreshDisplayData();
  },

  /**
   * 测试管理器初始化
   */
  async testManagerInitialization() {
    this.addTestResult('\n--- 测试管理器初始化 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 检查初始化状态
      if (resumeManager.isInitialized) {
        this.addTestResult('✅ 管理器已初始化');
      } else {
        this.addTestResult('⚠️ 管理器未初始化，正在初始化...');
        resumeManager.initialize();
      }
      
      // 检查是否有当前简历
      const currentResume = resumeManager.getCurrentResume();
      if (currentResume) {
        this.addTestResult('✅ 当前简历存在');
        this.addTestResult(`   简历ID: ${currentResume.id}`);
        this.addTestResult(`   简历标题: ${currentResume.title}`);
      } else {
        this.addTestResult('❌ 当前简历不存在');
      }
      
      // 获取统计信息
      const stats = resumeManager.getStats();
      this.addTestResult(`📊 统计信息: 总简历数 ${stats.totalResumes}`);
      
    } catch (error) {
      this.addTestResult('❌ 管理器初始化测试失败: ' + error.message);
    }
  },

  /**
   * 测试当前简历操作
   */
  async testCurrentResumeOperations() {
    this.addTestResult('\n--- 测试当前简历操作 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 获取当前简历
      const currentResume = resumeManager.getCurrentResume();
      this.addTestResult('✅ 获取当前简历成功');
      
      // 测试数据验证
      const errors = resumeManager.validateCurrentResume();
      this.addTestResult(`📝 数据验证结果: ${errors.length} 个错误`);
      if (errors.length > 0) {
        this.addTestResult(`   错误详情: ${errors.slice(0, 3).join(', ')}`);
      }
      
      // 测试JSON序列化
      const jsonData = resumeManager.getCurrentResumeJSON();
      this.addTestResult(`📄 JSON数据长度: ${jsonData.length} 字符`);
      
    } catch (error) {
      this.addTestResult('❌ 当前简历操作测试失败: ' + error.message);
    }
  },

  /**
   * 测试字段更新
   */
  async testFieldUpdates() {
    this.addTestResult('\n--- 测试字段更新 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 测试基本信息更新
      resumeManager.updateField('basicInfo.name', '测试用户');
      resumeManager.updateField('basicInfo.phone', '13800138000');
      this.addTestResult('✅ 基本信息字段更新成功');
      
      // 测试求职意向更新
      const jobIntentionData = {
        position: '前端开发工程师',
        salary: '10-15k',
        city: '北京',
        status: '在职-考虑机会'
      };
      resumeManager.updateField('jobIntention', jobIntentionData);
      this.addTestResult('✅ 求职意向更新成功');
      
      // 验证更新结果
      const currentResume = resumeManager.getCurrentResume();
      if (currentResume.basicInfo.name === '测试用户') {
        this.addTestResult('✅ 基本信息更新验证通过');
      } else {
        this.addTestResult('❌ 基本信息更新验证失败');
      }
      
      if (currentResume.jobIntention.position === '前端开发工程师') {
        this.addTestResult('✅ 求职意向更新验证通过');
      } else {
        this.addTestResult('❌ 求职意向更新验证失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 字段更新测试失败: ' + error.message);
    }
  },

  /**
   * 测试多简历管理
   */
  async testMultipleResumes() {
    this.addTestResult('\n--- 测试多简历管理 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 创建新简历
      const newResume = resumeManager.createNewResume('测试简历2');
      this.addTestResult('✅ 创建新简历成功');
      this.addTestResult(`   新简历ID: ${newResume.id}`);
      
      // 获取所有简历列表
      const allResumes = resumeManager.getAllResumes();
      this.addTestResult(`📋 简历列表: 共 ${allResumes.length} 个简历`);
      
      // 测试简历切换
      if (allResumes.length >= 2) {
        const firstResumeIndex = allResumes[1].index; // 切换到第二个简历
        const switchSuccess = resumeManager.switchToResume(firstResumeIndex);
        if (switchSuccess) {
          this.addTestResult('✅ 简历切换成功');
        } else {
          this.addTestResult('❌ 简历切换失败');
        }
      }
      
    } catch (error) {
      this.addTestResult('❌ 多简历管理测试失败: ' + error.message);
    }
  },

  /**
   * 测试数据持久化
   */
  async testDataPersistence() {
    this.addTestResult('\n--- 测试数据持久化 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 更新一些数据
      resumeManager.updateField('basicInfo.email', '<EMAIL>');
      this.addTestResult('✅ 数据更新完成（自动保存）');
      
      // 检查本地存储
      const savedCurrentIndex = wx.getStorageSync('currentResumeIndex');
      const savedResumeMap = wx.getStorageSync('resumeDataMap');
      
      if (savedCurrentIndex && savedResumeMap) {
        this.addTestResult('✅ 本地存储验证通过');
        this.addTestResult(`   当前简历索引: ${savedCurrentIndex}`);
        this.addTestResult(`   存储的简历数量: ${Object.keys(savedResumeMap).length}`);
      } else {
        this.addTestResult('❌ 本地存储验证失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 数据持久化测试失败: ' + error.message);
    }
  },

  /**
   * 刷新显示数据
   */
  refreshDisplayData() {
    try {
      const resumeManager = app.getResumeManager();
      
      // 获取当前简历数据
      const currentResume = resumeManager.getCurrentResume();
      
      // 获取所有简历列表
      const allResumes = resumeManager.getAllResumes();
      
      // 获取统计信息
      const managerStats = resumeManager.getStats();
      
      this.setData({
        currentResumeData: currentResume ? currentResume.toObject() : null,
        allResumes: allResumes,
        managerStats: managerStats
      });
      
    } catch (error) {
      console.error('刷新显示数据失败:', error);
    }
  },

  /**
   * 添加测试结果
   */
  addTestResult(message) {
    const timestamp = new Date().toLocaleTimeString();
    const result = {
      time: timestamp,
      message: message
    };
    
    this.setData({
      testResults: [...this.data.testResults, result]
    });
    
    console.log(`[${timestamp}] ${message}`);
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  /**
   * 重新运行测试
   */
  async rerunTests() {
    this.clearResults();
    await this.runTests();
  },

  /**
   * 查看当前简历详情
   */
  viewCurrentResume() {
    if (this.data.currentResumeData) {
      wx.showModal({
        title: '当前简历数据',
        content: JSON.stringify(this.data.currentResumeData, null, 2),
        showCancel: false,
        confirmText: '确定'
      });
    } else {
      wx.showToast({
        title: '没有简历数据',
        icon: 'none'
      });
    }
  },

  /**
   * 创建测试简历
   */
  createTestResume() {
    try {
      const resumeManager = app.getResumeManager();
      const newResume = resumeManager.createNewResume('手动创建的测试简历');
      
      wx.showToast({
        title: '创建成功',
        icon: 'success'
      });
      
      this.refreshDisplayData();
      this.addTestResult(`✅ 手动创建简历: ${newResume.id}`);
      
    } catch (error) {
      wx.showToast({
        title: '创建失败',
        icon: 'none'
      });
      this.addTestResult(`❌ 手动创建简历失败: ${error.message}`);
    }
  },

  /**
   * 测试求职意向页面
   */
  testJobIntentionPage() {
    wx.navigateTo({
      url: '/pages/makeResume/jobIntention/jobIntention'
    });
  }
});
