# 修正后的Token自动刷新方案

## 修正内容

根据您的反馈和API文档，我们对token管理进行了以下修正：

### 1. 修正刷新token接口调用
- **接口路径**: `/auth/refresh`
- **认证要求**: `needAuth: true`（需要当前token认证）
- **请求参数**: 空对象`{}`（不需要refresh_token）

### 2. 修正token字段格式
根据服务端返回格式：
```python
return WeChatLoginResponse(
    access_token=access_token,
    token_type="bearer", 
    expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    user_info=UserInfo.model_validate(current_user)
)
```

对应的tokenInfo结构：
```javascript
const tokenInfo = {
  access_token: tokenData.access_token,
  expires_in: tokenData.expires_in || 1800,
  token_type: tokenData.token_type || 'bearer',
  created_at: Date.now()
  // 移除了refresh_token字段
};
```

### 3. 简化刷新逻辑
- **移除refresh_token**: 不需要存储和使用refresh_token
- **简化刷新**: 用当前token调用`/auth/refresh`获取新token
- **替换token**: 刷新成功后直接替换当前token

## 核心工作机制

### 1. 定时刷新流程
```
保存token → 启动定时器 → 过期前5分钟 → 用当前token调用/auth/refresh → 获取新token → 替换当前token → 重新启动定时器
```

### 2. 401错误处理流程
```
API请求 → 服务器返回401 → 重新登录获取新token → 重试原请求 → 返回结果
```

## 修正后的代码

### 1. TokenManager核心方法

```javascript
/**
 * 保存token信息并启动定时刷新
 */
function saveTokenInfo(tokenData) {
  const tokenInfo = {
    access_token: tokenData.access_token,
    expires_in: tokenData.expires_in || 1800,
    token_type: tokenData.token_type || 'bearer',
    created_at: Date.now()
  };
  
  wx.setStorageSync('tokenInfo', tokenInfo);
  startTokenRefresh(tokenInfo.expires_in);
  return tokenInfo;
}

/**
 * 刷新token
 */
async function refreshToken() {
  const tokenInfo = getTokenInfo();
  if (!tokenInfo || !tokenInfo.access_token) {
    throw new Error('没有有效token，需要重新登录');
  }
  
  // 用当前token调用刷新接口
  const response = await userApi.refreshToken();
  
  // 保存新token（自动启动新的定时刷新）
  const newTokenInfo = saveTokenInfo(response);
  
  return newTokenInfo.access_token;
}
```

### 2. UserApi刷新方法

```javascript
/**
 * 刷新访问令牌
 */
function refreshToken() {
  return request.post('/auth/refresh', {}, {
    showLoading: false,
    needAuth: true // 需要当前token认证
  });
}
```

### 3. 登录时保存token

```javascript
// 登录成功后
const { access_token, user_info, expires_in, token_type } = response;

tokenManager.saveTokenInfo({
  access_token,
  expires_in,
  token_type
});
```

## 工作原理说明

### 1. 为什么不需要refresh_token？
- 服务端的`/auth/refresh`接口使用当前的`access_token`来验证身份
- 刷新时直接用当前token换取新token
- 这种方式更简单，不需要管理额外的refresh_token

### 2. 定时刷新的优势
- **预防性**: 在token过期前主动刷新，避免401错误
- **无感知**: 用户完全感知不到token更新过程
- **连续性**: 刷新成功后立即启动下一次定时刷新

### 3. 401错误兜底机制
- 如果定时刷新失败或网络问题导致token过期
- 401错误时自动重新登录获取新token
- 确保用户操作不会被中断

## 使用示例

### 1. 应用启动时
```javascript
// 自动登录成功后
const response = await userApi.login(code);
tokenManager.saveTokenInfo(response); // 自动启动定时刷新
```

### 2. 正常API调用
```javascript
// 用户调用任何API，系统自动处理token刷新和401错误
const userInfo = await userApi.getUserInfo();
const memberStatus = await userApi.getMemberStatus();
```

### 3. 退出登录时
```javascript
// 清除token并停止定时刷新
tokenManager.clearTokenInfo();
```

## 调试信息

### 1. 关键日志
```javascript
console.log('Token信息已保存，启动定时刷新');
console.log('将在X秒后刷新token');
console.log('开始刷新token...');
console.log('Token刷新成功');
console.log('检测到401错误，尝试重新登录后重试...');
```

### 2. 错误日志
```javascript
console.error('Token刷新失败:', error);
console.error('重新登录失败:', loginError);
```

## 优势总结

### 1. 简化性
- 移除了不必要的refresh_token管理
- 刷新逻辑更加直观：用当前token换新token
- 代码更简洁，易于理解

### 2. 可靠性
- 定时刷新预防大部分401错误
- 401错误时的自动重新登录兜底
- 刷新失败时的重试机制

### 3. 用户体验
- 完全无感知的token更新
- 不中断用户操作
- 快速的响应时间

### 4. 维护性
- 代码逻辑清晰简单
- 易于调试和排错
- 符合服务端API设计

这个修正后的方案完全符合您的服务端API设计，简化了token管理逻辑，同时保证了用户无感获得资源的目标。
