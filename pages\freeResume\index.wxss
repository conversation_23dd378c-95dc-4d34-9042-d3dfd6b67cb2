/* 免费模板页面样式 */
.free-resume-page {
  width: 100%;
  height: 100vh;
  background: #4B8BF5;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  display: flex;
  flex-direction: column;
}

/* 顶部标题栏 */
.header {
  background: #4B8BF5;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  padding: 20rpx 30rpx 30rpx 30rpx;
  color: white;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.subtitle-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.megaphone-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.subtitle-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  line-height: 1.4;
}

.subtitle-text .highlight {
  color: #ff6b35;
  font-weight: bold;
  font-size: 28rpx;
}

.subtitle-text .normal {
  color: #666;
  font-size: 24rpx;
  margin: 4rpx 0;
}

.action-text {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  text-align: center;
}

/* 模板列表容器 */
.template-list-container {
  flex: 1;
  background:rgb(255, 255, 255);
  border-radius: 0rpx 0rpx 0 0;
  margin-top: 20rpx;
  overflow: hidden;
  padding: 5rpx 20rpx 5rpx 10rpx;
}

.template-scroll {
  height: 100%;
  padding: 5rpx 5rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 模板网格 - 两列布局 */
.template-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding-bottom: 20rpx;
}

.template-item {
  background: white;
  border: 2rpx solid rgba(0,0,0,0.2);
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.template-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.template-image-container {
  position: relative;
  width: 100%;
  height: 492rpx;
  overflow: hidden;
}

.template-image {
  width: 100%;
  height: 100%;
  background: #f8f8f8;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #e0e0e0;
  border-top: 3rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.image-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 82, 82, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ff5252;
  border-radius: 8rpx;
}

.image-error-overlay .error-text {
  font-size: 20rpx;
  color: #ff5252;
  text-align: center;
}



/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.load-more-spinner {
  width: 30rpx;
  height: 30rpx;
  border: 2rpx solid #e0e0e0;
  border-top: 2rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

.load-more-text {
  color: #999;
  font-size: 24rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.no-more-text {
  color: #ccc;
  font-size: 24rpx;
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: 100rpx;
  left: 30rpx;
  right: 30rpx;
  background: #ff5252;
  color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  animation: slideDown 0.3s ease;
}

.error-text {
  flex: 1;
  font-size: 26rpx;
}

.error-close {
  background: none;
  border: none;
  color: white;
  font-size: 36rpx;
  padding: 0;
  margin: 0;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
