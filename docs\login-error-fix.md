# 登录错误修复总结

## 问题描述

用户反馈在小程序初始化登录时出现查询会员状态的401错误，错误日志显示：

1. **模块循环引用错误**: `TypeError: userApi.login is not a function`
2. **时序问题**: 主页加载时立即查询会员状态，但此时登录可能还未完成
3. **全局状态未更新**: 当检测到已有有效token时，没有更新全局状态

## 修复内容

### 1. 修复模块循环引用问题

**问题**: `tokenManager.js` 在顶部引用了 `userApi.js`，而 `userApi.js` 可能也引用了 `tokenManager.js`，导致运行时 `userApi.login` 未定义。

**解决方案**: 在函数内部动态引用，避免循环依赖

```javascript
// 修复前（错误）
const userApi = require('../api/userApi');

async function refreshToken() {
  const response = await userApi.refreshToken(); // userApi.login is not a function
}

// 修复后（正确）
async function refreshToken() {
  // 动态引用userApi避免循环依赖
  const userApi = require('../api/userApi');
  const response = await userApi.refreshToken();
}

async function autoReLogin() {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 动态引用userApi避免循环依赖
          const userApi = require('../api/userApi');
          userApi.login(loginRes.code)
            .then(resolve)
            .catch(reject);
        }
      }
    });
  });
}
```

### 2. 修复时序问题

**问题**: 主页在 `onLoad` 时立即查询会员状态，但此时自动登录可能还没有完成，导致401错误。

**解决方案**: 主页等待登录完成后再查询会员状态

```javascript
// 修复前（错误）
onLoad() {
  console.log('主页加载，查询会员状态...');
  // 立即查询，可能此时还没登录完成
  membershipManager.queryMemberStatus(false);
}

// 修复后（正确）
onLoad() {
  console.log('主页加载，等待登录完成后查询会员状态...');
  // 等待登录完成后再查询会员状态
  this.waitForLoginAndQueryMemberStatus();
}

async waitForLoginAndQueryMemberStatus() {
  try {
    // 等待登录完成
    await this.waitForLogin();
    
    console.log('登录完成，开始查询会员状态...');
    // 查询会员状态
    const membershipInfo = await membershipManager.queryMemberStatus(false);
    console.log('主页会员状态查询完成:', membershipInfo);
  } catch (err) {
    console.error('主页会员状态查询失败:', err);
  }
}

waitForLogin() {
  return new Promise((resolve) => {
    const checkLogin = () => {
      const app = getApp();
      if (app && app.globalData && app.globalData.userToken) {
        // 登录完成
        resolve();
      } else {
        // 继续等待，100ms后再检查
        setTimeout(checkLogin, 100);
      }
    };
    checkLogin();
  });
}
```

### 3. 修复全局状态未更新问题

**问题**: 当检测到已有有效token时，只是返回成功，但没有更新全局状态，导致主页检查时发现 `globalData.userToken` 为 null。

**解决方案**: 在验证token有效时，同时更新全局状态

```javascript
// 修复前（错误）
.then((isValid) => {
  if (isValid) {
    console.log('现有token有效，自动登录成功');
    resolve(true); // 只返回成功，没有更新全局状态
  }
})

// 修复后（正确）
.then((isValid) => {
  if (isValid) {
    console.log('现有token有效，更新全局状态...');
    
    // 更新全局状态
    const app = getApp();
    if (app) {
      app.globalData.userToken = existingToken;
      app.globalData.userId = existingUserId;
      app.globalData.hasUserInfo = true;
      
      // 如果有用户信息，也更新到全局状态
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        app.globalData.userInfo = userInfo;
        if (userInfo.hasOwnProperty('is_member')) {
          app.globalData.isMember = userInfo.is_member;
        }
      }
      
      console.log('全局状态已更新:', app.globalData);
    }
    
    console.log('现有token有效，自动登录成功');
    resolve(true);
  }
})
```

## 修复后的工作流程

### 1. 应用启动流程
```
App.onLaunch → autoLogin.performAutoLogin → 检查已有token → 更新全局状态 → 登录完成
```

### 2. 主页加载流程
```
Page.onLoad → waitForLogin → 检查globalData.userToken → 登录完成 → 查询会员状态
```

### 3. 401错误处理流程
```
API请求 → 401错误 → tokenManager.autoReLogin → 动态引用userApi → 重新登录 → 重试请求
```

## 关键修复点

### 1. 避免循环依赖
- 移除顶部的 `const userApi = require('../api/userApi')`
- 在需要时动态引用：`const userApi = require('../api/userApi')`

### 2. 确保时序正确
- 主页等待登录完成后再查询会员状态
- 使用轮询方式检查全局状态

### 3. 保持状态同步
- 验证token有效时更新全局状态
- 确保本地存储和全局状态一致

## 测试验证

修复后应该能看到以下正常日志：

```
开始执行自动登录...
检测到已有登录信息，验证token有效性...
现有token有效，更新全局状态...
全局状态已更新: {userToken: "xxx", userId: "xxx", hasUserInfo: true}
现有token有效，自动登录成功
主页加载，等待登录完成后查询会员状态...
登录完成，开始查询会员状态...
主页会员状态查询完成: {isMember: false}
```

## 总结

通过这次修复，我们解决了：

1. **模块循环引用问题** - 使用动态引用避免循环依赖
2. **时序问题** - 主页等待登录完成后再查询会员状态  
3. **状态同步问题** - 确保全局状态与本地存储一致

这些修复确保了用户在小程序启动时能够正常完成登录和会员状态查询，不会再出现401错误。
