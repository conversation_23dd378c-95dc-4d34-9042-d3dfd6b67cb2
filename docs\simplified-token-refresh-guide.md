# 精简Token自动刷新方案

## 方案概述

这是一个精简的token自动刷新解决方案，只包含两个核心功能：
1. **定时刷新token** - 在token过期前5分钟自动刷新
2. **401错误重试** - 当API返回401错误时，自动重新登录并重试请求

## 核心特性

### 1. 定时刷新机制
- 在保存token时自动启动定时器
- 在token过期前5分钟自动刷新
- 刷新成功后重新启动定时器
- 刷新失败时5分钟后重试

### 2. 401错误自动重试
- 检测到401错误时自动重新登录
- 重新登录成功后重试原请求
- 避免无限递归重试
- 用户无感知的错误恢复

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API请求       │    │   Request模块   │    │  TokenManager   │
│                 │    │                 │    │                 │
│ userApi.xxx()   │───▶│ executeRequest()│    │ saveTokenInfo() │
│ resumeApi.xxx() │    │ isAuthError()   │◀───│ autoReLogin()   │
│                 │    │ 401重试机制     │    │ 定时刷新        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 主要组件

### 1. TokenManager (`utils/auth/tokenManager.js`)

**核心方法**:
```javascript
saveTokenInfo(tokenData)    // 保存token并启动定时刷新
refreshToken()              // 刷新token
autoReLogin()               // 自动重新登录
clearTokenInfo()            // 清除token并停止定时刷新
```

**定时刷新逻辑**:
```javascript
// 在token过期前5分钟刷新
const refreshTime = Math.max((expiresIn - 300) * 1000, 60000);

refreshTimer = setTimeout(async () => {
  try {
    await refreshToken();
  } catch (error) {
    // 刷新失败，5分钟后重试
    setTimeout(() => startTokenRefresh(300), 5 * 60 * 1000);
  }
}, refreshTime);
```

### 2. Request模块 (`utils/api/request.js`)

**401错误处理**:
```javascript
async function request(options = {}) {
  try {
    return await executeRequest(options);
  } catch (error) {
    // 检测401错误并重试
    if (options.needAuth !== false && !options._isRetry && isAuthError(error)) {
      await tokenManager.autoReLogin();
      const retryOptions = { ...options, _isRetry: true };
      return await executeRequest(retryOptions);
    }
    throw error;
  }
}
```

## 工作流程

### 1. 正常请求流程
```
用户发起API请求 → 执行请求 → 返回结果
```

### 2. 定时刷新流程
```
保存token → 启动定时器 → 到期前5分钟 → 自动刷新 → 重新启动定时器
```

### 3. 401错误处理流程
```
API请求 → 服务器返回401 → 自动重新登录 → 重试原请求 → 返回结果
```

## 使用方法

### 1. 基本API调用
```javascript
// 所有API调用都会自动处理401错误
const userInfo = await userApi.getUserInfo();
const memberStatus = await userApi.getMemberStatus();
```

### 2. 登录时启动定时刷新
```javascript
// 在登录成功后调用
const response = await userApi.login(code);
tokenManager.saveTokenInfo(response); // 自动启动定时刷新
```

### 3. 退出时清理
```javascript
// 退出登录时调用
tokenManager.clearTokenInfo(); // 清除token并停止定时刷新
```

## 配置说明

### 1. 定时刷新配置
```javascript
// 在token过期前5分钟刷新
const REFRESH_BUFFER_TIME = 300; // 5分钟

// 最少1分钟后刷新（防止配置错误）
const MIN_REFRESH_TIME = 60000; // 1分钟
```

### 2. 重试配置
```javascript
// 刷新失败后的重试间隔
const RETRY_INTERVAL = 5 * 60 * 1000; // 5分钟
```

## 服务端要求

### 1. 登录接口响应
```json
{
  "access_token": "jwt_token",
  "refresh_token": "refresh_jwt_token",
  "expires_in": 1800,
  "user_info": {
    "id": 1,
    "nickname": "用户昵称"
  }
}
```

### 2. 刷新接口
```javascript
POST /auth/refresh
{
  "refresh_token": "refresh_jwt_token"
}
```

### 3. 401错误响应
```json
{
  "detail": "Token已过期",
  "error_code": "TOKEN_EXPIRED"
}
```

## 优势

### 1. 简单易懂
- 只有两个核心功能
- 代码逻辑清晰
- 易于维护和调试

### 2. 用户体验好
- 定时刷新避免请求时发现过期
- 401错误自动重试，用户无感知
- 不中断用户操作

### 3. 可靠性高
- 定时刷新失败会自动重试
- 401错误有兜底的重新登录机制
- 避免无限递归重试

### 4. 性能优化
- 预防性刷新减少401错误
- 最小化网络请求
- 及时清理定时器

## 调试和监控

### 1. 关键日志
```javascript
console.log('Token信息已保存，启动定时刷新');
console.log('将在X秒后刷新token');
console.log('定时刷新token...');
console.log('检测到401错误，尝试重新登录后重试...');
```

### 2. 错误处理
```javascript
console.error('定时刷新token失败:', error);
console.error('重新登录失败:', loginError);
```

## 最佳实践

### 1. 登录时启动
```javascript
// ✅ 推荐：登录成功后立即保存token
const response = await userApi.login(code);
tokenManager.saveTokenInfo(response);
```

### 2. 退出时清理
```javascript
// ✅ 推荐：退出时清理token和定时器
tokenManager.clearTokenInfo();
```

### 3. API调用
```javascript
// ✅ 推荐：正常调用API，系统自动处理401错误
const data = await userApi.getUserInfo();

// ❌ 避免：手动处理401错误
try {
  const data = await userApi.getUserInfo();
} catch (error) {
  if (error.statusCode === 401) {
    // 不需要手动处理
  }
}
```

## 总结

这个精简方案通过两个核心机制：
1. **定时刷新** - 预防token过期
2. **401重试** - 兜底处理过期情况

实现了用户无感知的token管理，既保证了安全性，又提供了良好的用户体验。方案简单可靠，易于理解和维护。
