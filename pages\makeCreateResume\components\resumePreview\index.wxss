/* 滚动预览容器 - 占满父容器高度 */
.resume-preview {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

/* 可滚动的图片容器 */
.scrollable-image-container {
  padding: 25rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 可滚动的预览图片样式 - 使用widthFix保持比例 */
.scrollable-preview-image {
  width: 100%;
  max-width: 750rpx;
  /* border-radius: 12rpx; */
  border: 2rpx solid #e0e0e0;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-top: 0rpx;
}

/* 加载动画容器 */
.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  margin: 0;
}

/* 错误状态容器 */
.error-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #fff5f5;
  margin: 0;
  border: 2rpx dashed #fecaca;
}

/* 简化的模板容器 */
.template-container {
  width: 100%;
  height: 100%;
  padding: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  border: 2rpx dashed #e5e7eb;
  /* border-radius: 16rpx; */
  margin: 20rpx;
  box-sizing: border-box;
}

/* 占位符文本 */
.placeholder-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 加载动画 */
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载文本样式 */
.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 错误图标样式 */
.error-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

/* 错误文本样式 */
.error-text {
  font-size: 28rpx;
  color: #dc2626;
  margin-bottom: 30rpx;
}

/* 重试按钮样式 */
.retry-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  /* border-radius: 8rpx; */
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.retry-button:active {
  background-color: #2563eb;
}

/* 图片加载覆盖层 */
.image-loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  /* border-radius: 12rpx; */
  padding: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.image-loading-overlay text {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
}

.resume-theme {
  --theme-color: #2B6CB0;
  --font-size: 28rpx;
  --spacing: 1;
}

