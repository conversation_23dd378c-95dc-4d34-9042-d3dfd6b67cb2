# 更新后的免费简历模板API接口文档

## 概述

根据微信小程序的需求，服务端API返回字段已简化，只包含必要的字段。

**基础URL**: `http://127.0.0.1:18080`

## 接口详情

### 1. 获取所有模板列表

**接口地址**: `GET /free-templates/`

**功能描述**: 获取所有免费简历模板列表

**请求参数**:
- `skip` (可选): 跳过的记录数，默认为0
- `limit` (可选): 返回的记录数，默认为20，最大1000
- `type` (可选): 文件类型筛选，如 "word"

**请求示例**:
```
GET /free-templates/?skip=0&limit=20&type=word
```

**响应示例**:
```json
{
  "total": 10,
  "templates": [
    {
      "id": "coverResume/24.jpg",
      "thumb_url": "http://127.0.0.1:18080/static/free_resume_templates/coverResume/24.jpg",
      "baidu_url": "https://pan.baidu.com/s/1234567890",
      "baidu_pass": "abc123",
      "quark_url": "https://pan.quark.cn/s/1234567890",
      "quark_pass": "def456",
      "type": "word"
    },
    {
      "id": "coverResume/25.jpg",
      "thumb_url": "http://127.0.0.1:18080/static/free_resume_templates/coverResume/25.jpg",
      "baidu_url": "https://pan.baidu.com/s/0987654321",
      "baidu_pass": "xyz789",
      "quark_url": "https://pan.quark.cn/s/0987654321",
      "quark_pass": "uvw012",
      "type": "word"
    }
  ]
}
```

## 数据模型

### 简化后的 FreeTemplate 模型

| 字段名 | 类型 | 是否必需 | 描述 | 示例 |
|--------|------|----------|------|------|
| id | string | 是 | 模板ID（主键） | "coverResume/24.jpg" |
| thumb_url | string | 是 | 缩略图完整URL | "http://127.0.0.1:18080/static/free_resume_templates/coverResume/24.jpg" |
| baidu_url | string | 否 | 百度网盘链接 | "https://pan.baidu.com/s/1234567890" |
| baidu_pass | string | 否 | 百度网盘提取码 | "abc123" |
| quark_url | string | 否 | 夸克网盘链接 | "https://pan.quark.cn/s/1234567890" |
| quark_pass | string | 否 | 夸克网盘提取码 | "def456" |
| type | string | 是 | 文件类型 | "word" |

## 重要变更说明

### 移除的字段
以下字段已从API响应中移除：
- `batch_flag` - 批次标识
- `thumb_path` - 缩略图相对路径
- `download_count` - 下载次数
- `created_at` - 创建时间
- `updated_at` - 更新时间

### 保留的字段
- `id` - 模板唯一标识
- `thumb_url` - 缩略图完整URL（微信端直接使用）
- `baidu_url` - 百度网盘下载链接
- `baidu_pass` - 百度网盘提取码
- `quark_url` - 夸克网盘下载链接
- `quark_pass` - 夸克网盘提取码
- `type` - 文件类型

## 服务端实现建议

### Python FastAPI 示例

```python
from pydantic import BaseModel
from typing import List, Optional

class FreeTemplate(BaseModel):
    id: str
    thumb_url: str
    baidu_url: Optional[str] = None
    baidu_pass: Optional[str] = None
    quark_url: Optional[str] = None
    quark_pass: Optional[str] = None
    type: str

class FreeTemplateListResponse(BaseModel):
    total: int
    templates: List[FreeTemplate]

@app.get("/free-templates/", response_model=FreeTemplateListResponse)
async def get_free_templates(
    skip: int = 0,
    limit: int = 20,
    type: Optional[str] = None
):
    # 查询数据库
    templates = await query_templates(skip=skip, limit=limit, type=type)
    total = await count_templates(type=type)
    
    # 构建响应
    template_list = []
    for template in templates:
        template_data = {
            "id": template.id,
            "thumb_url": f"http://127.0.0.1:18080/static/{template.thumb_path}",
            "baidu_url": template.baidu_url,
            "baidu_pass": template.baidu_pass,
            "quark_url": template.quark_url,
            "quark_pass": template.quark_pass,
            "type": template.type
        }
        template_list.append(template_data)
    
    return FreeTemplateListResponse(
        total=total,
        templates=template_list
    )
```

## 错误处理

**常见错误**:
- `500 Internal Server Error`: 服务器内部错误，通常是数据验证失败
- `404 Not Found`: 请求的资源不存在
- `400 Bad Request`: 请求参数错误

**错误响应格式**:
```json
{
  "detail": "错误描述信息"
}
```

## 测试建议

使用以下命令测试API：

```bash
# 测试获取模板列表
curl "http://127.0.0.1:18080/free-templates/?skip=0&limit=5&type=word"

# 预期响应应该包含简化后的字段结构
```
