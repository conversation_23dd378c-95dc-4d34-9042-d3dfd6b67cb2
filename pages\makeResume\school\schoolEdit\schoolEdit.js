const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    schoolEditFormData: null, // 将在 onLoad 中初始化为 SchoolExperienceItem 实例
    editIndex: -1
  },

  onLoad(options) {
    console.log('=== 在校经历编辑页面加载 ===');

    if (options.index !== undefined) {
      // 编辑模式
      this.loadSchoolItemForEdit(parseInt(options.index));
    } else {
      // 新增模式
      this.initNewSchoolItem();
    }
  },

  /**
   * 加载在校经历项进行编辑
   */
  loadSchoolItemForEdit(index) {
    try {
      const schoolData = ResumeFormHelper.loadFieldData('school', app);

      if (schoolData && schoolData[index]) {
        const item = schoolData[index];
        this.setData({
          schoolEditFormData: item,
          editIndex: index
        });
        console.log('✅ 在校经历编辑数据加载成功:', item);
      } else {
        console.error('❌ 在校经历数据不存在，索引:', index);
        this.initNewSchoolItem();
      }
    } catch (error) {
      console.error('❌ 加载在校经历编辑数据失败:', error);
      this.initNewSchoolItem();
    }
  },

  /**
   * 初始化新的在校经历项
   */
  initNewSchoolItem() {
    const emptySchoolItem = ResumeFormHelper.getEmptyFieldData('schoolExperienceItem');
    this.setData({
      schoolEditFormData: emptySchoolItem,
      editIndex: -1
    });
    console.log('✅ 新在校经历项初始化完成');
  },



  // 处理输入框内容变化
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`schoolEditFormData.${field}`]: value
    });
  },



  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;

    this.setData({
      [`schoolEditFormData.${field}`]: value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'schoolEditFormData.endDate': '至今'
    });
  },

  /**
   * 保存在校经历信息
   */
  saveSchool() {
    const { schoolEditFormData, editIndex } = this.data;

    // 验证必填字段
    if (!schoolEditFormData.role || !schoolEditFormData.startDate) {
      wx.showToast({
        title: '请填写必填信息',
        icon: 'none'
      });
      return;
    }

    try {
      const schoolData = ResumeFormHelper.loadFieldData('school', app);
      let schoolList = Array.isArray(schoolData) ? [...schoolData] : [];

      if (editIndex >= 0) {
        // 更新已有记录
        schoolList[editIndex] = schoolEditFormData;
      } else {
        // 添加新记录
        schoolList.push(schoolEditFormData);
      }

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('school', schoolList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        console.log('✅ 在校经历保存成功:', schoolEditFormData);

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 在校经历保存失败');
      }
    } catch (error) {
      console.error('❌ 保存在校经历时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除在校经历
   */
  deleteSchool() {
    const { editIndex } = this.data;
    if (editIndex >= 0) {
      wx.showModal({
        title: '提示',
        content: '确定删除这条在校经历吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              const schoolData = ResumeFormHelper.loadFieldData('school', app);
              let schoolList = Array.isArray(schoolData) ? [...schoolData] : [];

              schoolList.splice(editIndex, 1);

              // 使用 ResumeFormHelper 统一保存
              const success = ResumeFormHelper.saveFieldData('school', schoolList, app);

              if (success) {
                wx.navigateBack({
                  success: () => {
                    wx.showToast({
                      title: '删除成功',
                      icon: 'success'
                    });
                  }
                });
                console.log('✅ 在校经历删除成功');
              } else {
                wx.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
                console.error('❌ 在校经历删除失败');
              }
            } catch (error) {
              console.error('❌ 删除在校经历时发生错误:', error);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
});