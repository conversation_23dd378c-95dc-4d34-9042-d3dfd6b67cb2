.container2 {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 140rpx;
}

.formItem2 {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.label2 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.datePicker2 {
  display: flex;
  align-items: center;
}

.pickerText2 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.separator2 {
  padding: 0 20rpx;
  color: #999;
}

.toNow2 {
  padding: 10rpx 20rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.input2 {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.contentArea2 {
  margin-bottom: 0;
}



.contentInput2 {
  width: 100%;
  min-height: 300rpx;
  font-size: 28rpx;
  line-height: 1.6;
  padding: 20rpx 0;
}

.buttonGroup2 {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.saveBtn2, .deleteBtn2 {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.saveBtn2 {
  flex: 3;
  background: #4B8BF5;
  color: #fff;
  margin-right: 20rpx;
}

.deleteBtn2 {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4rpx;
}