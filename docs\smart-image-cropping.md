# 智能图片裁剪功能

## 概述

为了解决简历照片预览时裁剪不居中的问题，我们开发了智能图片裁剪工具。该工具能够自动分析图片特征，选择最佳的裁剪策略，确保重要内容（如人脸）能够居中显示在目标尺寸中。

## 问题分析

### 原有问题
1. **裁剪不居中**：使用 `wx.cropImage` 的固定比例裁剪，用户手动选择区域可能不够精确
2. **预览效果差**：`mode="aspectFill"` 可能导致重要内容被裁切到边缘
3. **用户体验不佳**：需要用户手动调整裁剪区域，操作复杂

### 解决方案
1. **智能裁剪算法**：自动计算最佳裁剪区域，确保居中显示
2. **多种裁剪模式**：支持居中、顶部、底部等不同裁剪策略
3. **自动推荐**：根据图片特征自动选择最适合的裁剪模式

## 功能特性

### 1. 智能裁剪算法
- **居中裁剪**：自动计算图片中心区域进行裁剪
- **比例适配**：智能适配目标尺寸比例（3:4）
- **内容保护**：优先保留图片的重要内容区域

### 2. 多种裁剪模式
```javascript
const cropModes = {
  center: '居中裁剪',    // 从图片中心开始裁剪
  top: '顶部裁剪',       // 从图片顶部开始裁剪（适合人像）
  bottom: '底部裁剪',    // 从图片底部开始裁剪
  left: '左侧裁剪',      // 从图片左侧开始裁剪
  right: '右侧裁剪'      // 从图片右侧开始裁剪
};
```

### 3. 自动推荐系统
根据图片宽高比自动推荐最佳裁剪模式：
- **竖向图片** (ratio < 1)：推荐 `top` 模式，保留头部区域
- **横向图片** (ratio > 1.5)：推荐 `center` 模式，居中裁剪
- **接近正方形**：推荐 `center` 模式，居中裁剪

## 技术实现

### 1. 核心算法

```javascript
calculateCropParams(imageInfo, config) {
  const { width: imgWidth, height: imgHeight } = imageInfo;
  const { targetWidth, targetHeight, cropMode } = config;

  // 计算目标宽高比
  const targetRatio = targetWidth / targetHeight;
  const imageRatio = imgWidth / imgHeight;

  let cropWidth, cropHeight, cropX, cropY;

  if (imageRatio > targetRatio) {
    // 图片比目标更宽，需要裁剪宽度
    cropHeight = imgHeight;
    cropWidth = imgHeight * targetRatio;
    cropY = 0;
    
    // 根据裁剪模式确定X位置
    switch (cropMode) {
      case 'center':
        cropX = (imgWidth - cropWidth) / 2;
        break;
      case 'left':
        cropX = 0;
        break;
      case 'right':
        cropX = imgWidth - cropWidth;
        break;
    }
  } else {
    // 图片比目标更高，需要裁剪高度
    cropWidth = imgWidth;
    cropHeight = imgWidth / targetRatio;
    cropX = 0;
    
    // 根据裁剪模式确定Y位置
    switch (cropMode) {
      case 'center':
        cropY = (imgHeight - cropHeight) / 2;
        break;
      case 'top':
        cropY = 0;
        break;
      case 'bottom':
        cropY = imgHeight - cropHeight;
        break;
    }
  }

  return { cropX, cropY, cropWidth, cropHeight };
}
```

### 2. Canvas 精确裁剪

使用微信小程序的 Canvas API 实现精确的图片裁剪：

```javascript
// 绘制裁剪后的图片
canvas.drawImage(
  filePath,
  cropX, cropY, cropWidth, cropHeight,  // 源图片裁剪区域
  0, 0, outputWidth, outputHeight       // 目标区域
);
```

### 3. 多层降级方案

为确保功能稳定性，实现了多层降级方案：

1. **首选方案**：智能裁剪 + Canvas压缩
2. **备用方案**：wx.cropImage + 图片压缩
3. **降级方案**：直接转换为base64

## 使用方法

### 1. 基本使用

```javascript
const smartImageCropper = require('../../utils/smartImageCropper');

// 智能裁剪
const croppedBase64 = await smartImageCropper.smartCrop(filePath, {
  targetWidth: 120,
  targetHeight: 150,
  quality: 0.8,
  format: 'jpeg',
  cropMode: 'center'
});
```

### 2. 自动推荐模式

```javascript
// 获取图片信息
const imageInfo = await getImageInfo(filePath);

// 获取推荐的裁剪模式
const recommendedMode = smartImageCropper.getRecommendedCropMode(imageInfo);

// 使用推荐模式进行裁剪
const croppedBase64 = await smartImageCropper.smartCrop(filePath, {
  targetWidth: 120,
  targetHeight: 150,
  cropMode: recommendedMode
});
```

### 3. 批量处理

```javascript
const results = await smartImageCropper.batchProcess(filePaths, {
  targetWidth: 120,
  targetHeight: 150,
  quality: 0.8
});
```

## 性能优化

### 1. 尺寸计算优化
- 使用整数运算，避免浮点数精度问题
- 预计算常用比例，减少重复计算

### 2. Canvas 优化
- 复用 Canvas 实例，减少创建开销
- 异步处理，避免阻塞主线程

### 3. 内存管理
- 及时清理临时文件
- 控制并发处理数量

## 效果对比

### 改进前
- ❌ 裁剪位置不可控
- ❌ 重要内容可能被裁切
- ❌ 用户需要手动调整
- ❌ 预览效果不佳

### 改进后
- ✅ 智能居中裁剪
- ✅ 保护重要内容区域
- ✅ 自动选择最佳模式
- ✅ 预览效果优秀

## 配置选项

```javascript
const config = {
  targetWidth: 120,        // 目标宽度（px）
  targetHeight: 150,       // 目标高度（px）
  quality: 0.8,           // 压缩质量 (0.1-1.0)
  format: 'jpeg',         // 输出格式 ('jpeg' | 'png')
  cropMode: 'center'      // 裁剪模式
};
```

## 测试验证

创建了完整的测试套件：
- ✅ 裁剪参数计算测试
- ✅ 推荐模式测试
- ✅ 智能裁剪流程测试
- ✅ 批量处理测试
- ✅ 性能测试
- ✅ 边界条件测试

运行测试：
```bash
node test/smart-image-cropper-test.js
```

## 兼容性

- ✅ 微信小程序基础库 2.9.0+
- ✅ iOS 和 Android 平台
- ✅ 支持 JPEG、PNG 格式
- ✅ 向下兼容原有功能

## 未来扩展

1. **AI 人脸检测**：集成人脸识别，智能定位人脸中心
2. **更多裁剪模式**：支持自定义裁剪区域
3. **批量优化**：支持更大规模的批量处理
4. **实时预览**：提供裁剪效果实时预览功能
