/* components/customNavBar/customNavBar.wxss */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  width: 100%;
}

.status-bar {
  width: 100%;
}

.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15rpx;
  width: 100%;
  box-sizing: border-box;
}

.nav-left,
.nav-right {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.nav-left {
  justify-content: flex-start;
}

.nav-right {
  justify-content: flex-end;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin: 0 5rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s;
}

.nav-btn:active {
  background-color: rgba(255, 255, 255, 0.2);
}

.nav-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.back-btn .nav-icon {
  font-size: 48rpx;
  margin-left: -4rpx;
}

.share-btn .nav-icon {
  font-size: 28rpx;
}

.feedback-btn .nav-icon {
  font-size: 28rpx;
}
