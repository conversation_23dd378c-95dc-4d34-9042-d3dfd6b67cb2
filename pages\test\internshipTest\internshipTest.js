/**
 * 实习经历模块测试页面
 * 测试改造后的实习经历功能
 */

const app = getApp();
const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');

Page({
  data: {
    testResults: [],
    currentInternshipList: [],
    testInternshipData: {
      company: '测试公司',
      position: '测试职位',
      startDate: '2023-01',
      endDate: '2023-06',
      content: '这是一个测试实习经历的描述内容。'
    }
  },

  onLoad() {
    console.log('=== 实习经历模块测试页面加载 ===');
    this.runInternshipTests();
  },

  /**
   * 运行实习经历测试
   */
  async runInternshipTests() {
    this.addTestResult('开始测试实习经历模块...');
    
    await this.testInternshipItemCreation();
    await this.testInternshipDataOperations();
    await this.testInternshipValidation();
    await this.testInternshipArrayOperations();
    
    this.addTestResult('✅ 实习经历模块测试完成');
    this.refreshInternshipList();
  },

  /**
   * 测试实习经历项创建
   */
  async testInternshipItemCreation() {
    this.addTestResult('\n--- 测试实习经历项创建 ---');
    
    try {
      // 测试空实习经历项创建
      const emptyInternship = ResumeFormHelper.getEmptyFieldData('internshipItem');
      this.addTestResult('✅ 空实习经历项创建成功');
      this.addTestResult(`   字段数量: ${Object.keys(emptyInternship).length}`);
      this.addTestResult(`   字段: ${Object.keys(emptyInternship).join(', ')}`);
      
      // 测试带数据的实习经历项创建
      const testInternship = ResumeFormHelper.getEmptyFieldData('internshipItem');
      Object.assign(testInternship, this.data.testInternshipData);
      this.addTestResult('✅ 带数据实习经历项创建成功');
      this.addTestResult(`   公司: ${testInternship.company}`);
      this.addTestResult(`   职位: ${testInternship.position}`);
      
    } catch (error) {
      this.addTestResult('❌ 实习经历项创建测试失败: ' + error.message);
    }
  },

  /**
   * 测试实习经历数据操作
   */
  async testInternshipDataOperations() {
    this.addTestResult('\n--- 测试实习经历数据操作 ---');
    
    try {
      // 测试加载实习经历数据
      const internshipList = ResumeFormHelper.loadFieldData('internship', app);
      this.addTestResult('✅ 实习经历数据加载成功');
      this.addTestResult(`   当前实习经历数量: ${Array.isArray(internshipList) ? internshipList.length : 0}`);
      
      // 测试保存实习经历数据
      const testList = [
        { ...this.data.testInternshipData, company: '测试公司1' },
        { ...this.data.testInternshipData, company: '测试公司2' }
      ];
      
      const saveSuccess = ResumeFormHelper.saveFieldData('internship', testList, app);
      if (saveSuccess) {
        this.addTestResult('✅ 实习经历数据保存成功');
        this.addTestResult(`   保存的实习经历数量: ${testList.length}`);
      } else {
        this.addTestResult('❌ 实习经历数据保存失败');
      }
      
      // 验证保存结果
      const savedList = ResumeFormHelper.loadFieldData('internship', app);
      if (Array.isArray(savedList) && savedList.length === testList.length) {
        this.addTestResult('✅ 实习经历数据保存验证通过');
      } else {
        this.addTestResult('❌ 实习经历数据保存验证失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 实习经历数据操作测试失败: ' + error.message);
    }
  },

  /**
   * 测试实习经历验证
   */
  async testInternshipValidation() {
    this.addTestResult('\n--- 测试实习经历验证 ---');
    
    try {
      // 测试有效数据验证
      const validData = this.data.testInternshipData;
      const validErrors = ResumeFormHelper.validateFieldData('internshipItem', validData);
      this.addTestResult(`✅ 有效数据验证: ${validErrors.length} 个错误`);
      
      // 测试无效数据验证
      const invalidData = {
        company: '',
        position: '',
        startDate: '',
        endDate: '',
        content: ''
      };
      
      const invalidErrors = ResumeFormHelper.validateFieldData('internshipItem', invalidData);
      this.addTestResult(`📝 无效数据验证: ${invalidErrors.length} 个错误`);
      if (invalidErrors.length > 0) {
        this.addTestResult(`   错误详情: ${invalidErrors.slice(0, 2).join(', ')}`);
      }
      
    } catch (error) {
      this.addTestResult('❌ 实习经历验证测试失败: ' + error.message);
    }
  },

  /**
   * 测试实习经历数组操作
   */
  async testInternshipArrayOperations() {
    this.addTestResult('\n--- 测试实习经历数组操作 ---');
    
    try {
      // 获取当前实习经历列表
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();
      let internshipList = currentResume.internship ? 
        currentResume.internship.map(item => item.toObject()) : [];
      
      this.addTestResult(`📋 当前实习经历数量: ${internshipList.length}`);
      
      // 测试添加实习经历
      const newInternship = { 
        ...this.data.testInternshipData, 
        company: '新增测试公司',
        position: '新增测试职位'
      };
      internshipList.push(newInternship);
      
      const addSuccess = ResumeFormHelper.saveFieldData('internship', internshipList, app);
      if (addSuccess) {
        this.addTestResult('✅ 添加实习经历成功');
        this.addTestResult(`   新的数量: ${internshipList.length}`);
      } else {
        this.addTestResult('❌ 添加实习经历失败');
      }
      
      // 测试修改实习经历
      if (internshipList.length > 0) {
        internshipList[0].company = '修改后的公司名称';
        const updateSuccess = ResumeFormHelper.saveFieldData('internship', internshipList, app);
        if (updateSuccess) {
          this.addTestResult('✅ 修改实习经历成功');
        } else {
          this.addTestResult('❌ 修改实习经历失败');
        }
      }
      
      // 测试删除实习经历
      if (internshipList.length > 0) {
        const originalLength = internshipList.length;
        internshipList.splice(0, 1);
        const deleteSuccess = ResumeFormHelper.saveFieldData('internship', internshipList, app);
        if (deleteSuccess) {
          this.addTestResult('✅ 删除实习经历成功');
          this.addTestResult(`   删除后数量: ${internshipList.length} (原来: ${originalLength})`);
        } else {
          this.addTestResult('❌ 删除实习经历失败');
        }
      }
      
    } catch (error) {
      this.addTestResult('❌ 实习经历数组操作测试失败: ' + error.message);
    }
  },

  /**
   * 刷新实习经历列表显示
   */
  refreshInternshipList() {
    try {
      const internshipList = ResumeFormHelper.loadFieldData('internship', app);
      this.setData({
        currentInternshipList: Array.isArray(internshipList) ? internshipList : []
      });
    } catch (error) {
      console.error('刷新实习经历列表失败:', error);
    }
  },

  /**
   * 添加测试结果
   */
  addTestResult(message) {
    const timestamp = new Date().toLocaleTimeString();
    const result = {
      time: timestamp,
      message: message
    };
    
    this.setData({
      testResults: [...this.data.testResults, result]
    });
    
    console.log(`[${timestamp}] ${message}`);
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  /**
   * 重新运行测试
   */
  async rerunTests() {
    this.clearResults();
    await this.runInternshipTests();
  },

  /**
   * 测试实习经历列表页面
   */
  testInternshipListPage() {
    wx.navigateTo({
      url: '/pages/makeResume/internship/internshipExperience/internshipExperience'
    });
  },

  /**
   * 测试实习经历编辑页面（新增）
   */
  testInternshipEditPageAdd() {
    wx.navigateTo({
      url: '/pages/makeResume/internship/internshipEdit/internshipEdit'
    });
  },

  /**
   * 测试实习经历编辑页面（编辑）
   */
  testInternshipEditPageEdit() {
    if (this.data.currentInternshipList.length > 0) {
      wx.navigateTo({
        url: '/pages/makeResume/internship/internshipEdit/internshipEdit?index=0'
      });
    } else {
      wx.showToast({
        title: '没有实习经历可编辑',
        icon: 'none'
      });
    }
  },

  /**
   * 手动添加测试数据
   */
  addTestData() {
    try {
      const internshipList = ResumeFormHelper.loadFieldData('internship', app);
      const currentList = Array.isArray(internshipList) ? internshipList : [];
      
      const newInternship = {
        ...this.data.testInternshipData,
        company: `手动测试公司${currentList.length + 1}`,
        position: `手动测试职位${currentList.length + 1}`
      };
      
      currentList.push(newInternship);
      
      const success = ResumeFormHelper.saveFieldData('internship', currentList, app);
      
      if (success) {
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
        
        this.addTestResult(`✅ 手动添加实习经历: ${newInternship.company}`);
        this.refreshInternshipList();
      } else {
        wx.showToast({
          title: '添加失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
      this.addTestResult(`❌ 手动添加实习经历失败: ${error.message}`);
    }
  },

  /**
   * 清空所有实习经历
   */
  clearAllInternships() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有实习经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            const success = ResumeFormHelper.saveFieldData('internship', [], app);
            
            if (success) {
              wx.showToast({
                title: '清空成功',
                icon: 'success'
              });
              
              this.addTestResult('✅ 清空所有实习经历成功');
              this.refreshInternshipList();
            } else {
              wx.showToast({
                title: '清空失败',
                icon: 'none'
              });
            }
            
          } catch (error) {
            wx.showToast({
              title: '清空失败',
              icon: 'none'
            });
            this.addTestResult(`❌ 清空实习经历失败: ${error.message}`);
          }
        }
      }
    });
  }
});
