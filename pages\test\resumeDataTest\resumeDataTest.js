/**
 * 简历数据测试页面
 * 用于测试新的统一简历数据管理系统
 */

const globalResumeManager = require('../../../utils/resume/GlobalResumeManager.js');
const { ResumeData } = require('../../../utils/resume/ResumeData.js');

Page({
  data: {
    testResults: [],
    currentResumeData: null,
    isManagerInitialized: false
  },

  async onLoad() {
    console.log('=== 简历数据测试页面加载 ===');
    await this.initializeManager();
    this.runTests();
  },

  /**
   * 初始化管理器
   */
  async initializeManager() {
    try {
      this.addTestResult('开始初始化全局简历管理器...');
      await globalResumeManager.initialize();
      
      this.setData({
        isManagerInitialized: true
      });
      
      this.addTestResult('✅ 全局简历管理器初始化成功');
      
      // 添加数据变更监听器
      globalResumeManager.addListener('testPage', (action, data) => {
        this.addTestResult(`📢 数据变更通知: ${action}`, data);
      });
      
    } catch (error) {
      this.addTestResult('❌ 管理器初始化失败: ' + error.message);
    }
  },

  /**
   * 运行测试
   */
  async runTests() {
    this.addTestResult('\n=== 开始运行测试 ===');
    
    await this.testDataCreation();
    await this.testDataValidation();
    await this.testDataSerialization();
    await this.testManagerOperations();
    await this.testDataMigration();
    
    this.addTestResult('\n=== 测试完成 ===');
  },

  /**
   * 测试数据创建
   */
  async testDataCreation() {
    this.addTestResult('\n--- 测试数据创建 ---');
    
    try {
      // 测试空数据创建
      const emptyResume = new ResumeData();
      this.addTestResult('✅ 空简历创建成功');
      
      // 测试带数据创建
      const testData = {
        basicInfo: {
          name: '张三',
          phone: '13800138000',
          email: '<EMAIL>'
        },
        jobIntention: {
          position: '前端开发工程师',
          salary: '10-15K'
        },
        education: [{
          school: '清华大学',
          major: '计算机科学与技术',
          degree: '本科',
          startDate: '2018-09',
          endDate: '2022-06'
        }]
      };
      
      const resumeWithData = new ResumeData(testData);
      this.addTestResult('✅ 带数据简历创建成功');
      this.addTestResult(`   姓名: ${resumeWithData.basicInfo.name}`);
      this.addTestResult(`   职位: ${resumeWithData.jobIntention.position}`);
      this.addTestResult(`   教育经历数量: ${resumeWithData.education.length}`);
      
    } catch (error) {
      this.addTestResult('❌ 数据创建测试失败: ' + error.message);
    }
  },

  /**
   * 测试数据验证
   */
  async testDataValidation() {
    this.addTestResult('\n--- 测试数据验证 ---');
    
    try {
      // 测试空数据验证
      const emptyResume = new ResumeData();
      const emptyErrors = emptyResume.validate();
      this.addTestResult(`空简历验证错误数量: ${emptyErrors.length}`);
      
      // 测试完整数据验证
      const completeData = {
        basicInfo: {
          name: '李四',
          phone: '13900139000'
        },
        education: [{
          school: '北京大学',
          major: '软件工程',
          degree: '硕士'
        }]
      };
      
      const completeResume = new ResumeData(completeData);
      const completeErrors = completeResume.validate();
      this.addTestResult(`完整简历验证错误数量: ${completeErrors.length}`);
      
      if (completeErrors.length === 0) {
        this.addTestResult('✅ 数据验证测试通过');
      } else {
        this.addTestResult('⚠️ 验证发现问题: ' + completeErrors.join(', '));
      }
      
    } catch (error) {
      this.addTestResult('❌ 数据验证测试失败: ' + error.message);
    }
  },

  /**
   * 测试数据序列化
   */
  async testDataSerialization() {
    this.addTestResult('\n--- 测试数据序列化 ---');
    
    try {
      const testData = {
        basicInfo: {
          name: '王五',
          phone: '13700137000'
        },
        work: [{
          company: '腾讯',
          position: '高级前端工程师',
          startDate: '2020-01',
          endDate: '2023-12'
        }]
      };
      
      const originalResume = new ResumeData(testData);
      
      // 测试转换为对象
      const obj = originalResume.toObject();
      this.addTestResult('✅ 转换为对象成功');
      
      // 测试转换为JSON
      const jsonStr = originalResume.toJSON();
      this.addTestResult('✅ 转换为JSON成功');
      this.addTestResult(`   JSON长度: ${jsonStr.length} 字符`);
      
      // 测试从JSON恢复
      const restoredResume = ResumeData.fromJSON(jsonStr);
      this.addTestResult('✅ 从JSON恢复成功');
      
      // 验证数据一致性
      if (restoredResume.basicInfo.name === originalResume.basicInfo.name &&
          restoredResume.work.length === originalResume.work.length) {
        this.addTestResult('✅ 数据序列化一致性验证通过');
      } else {
        this.addTestResult('❌ 数据序列化一致性验证失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 数据序列化测试失败: ' + error.message);
    }
  },

  /**
   * 测试管理器操作
   */
  async testManagerOperations() {
    this.addTestResult('\n--- 测试管理器操作 ---');
    
    try {
      // 测试获取当前简历
      const currentResume = globalResumeManager.getCurrentResume();
      this.addTestResult('✅ 获取当前简历成功');
      this.addTestResult(`   简历ID: ${currentResume.id || '未设置'}`);
      
      // 测试字段更新
      const updateSuccess = globalResumeManager.updateField('basicInfo.name', '测试用户');
      if (updateSuccess) {
        this.addTestResult('✅ 字段更新成功');
      } else {
        this.addTestResult('❌ 字段更新失败');
      }
      
      // 测试保存
      const saveSuccess = await globalResumeManager.saveCurrentResume();
      if (saveSuccess) {
        this.addTestResult('✅ 简历保存成功');
      } else {
        this.addTestResult('❌ 简历保存失败');
      }
      
      // 测试创建新简历
      const newResume = globalResumeManager.createNewResume('测试简历');
      this.addTestResult('✅ 创建新简历成功');
      this.addTestResult(`   新简历ID: ${newResume.id}`);
      
    } catch (error) {
      this.addTestResult('❌ 管理器操作测试失败: ' + error.message);
    }
  },

  /**
   * 测试数据迁移
   */
  async testDataMigration() {
    this.addTestResult('\n--- 测试数据迁移 ---');
    
    try {
      // 模拟旧数据
      const legacyData = {
        basicInfo: { name: '迁移测试用户', phone: '13600136000' },
        education: [{ school: '测试大学', major: '测试专业', degree: '本科' }],
        workList: [{ company: '测试公司', position: '测试职位' }]
      };
      
      // 保存到旧的存储键
      wx.setStorageSync('basicInfo', legacyData.basicInfo);
      wx.setStorageSync('education', legacyData.education);
      wx.setStorageSync('workList', legacyData.workList);
      
      this.addTestResult('✅ 模拟旧数据保存完成');
      
      // 重新初始化管理器以触发迁移
      globalResumeManager.reset();
      await globalResumeManager.initialize();
      
      const migratedResume = globalResumeManager.getCurrentResume();
      
      if (migratedResume.basicInfo.name === legacyData.basicInfo.name) {
        this.addTestResult('✅ 数据迁移成功');
        this.addTestResult(`   迁移后姓名: ${migratedResume.basicInfo.name}`);
      } else {
        this.addTestResult('❌ 数据迁移失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 数据迁移测试失败: ' + error.message);
    }
  },

  /**
   * 添加测试结果
   */
  addTestResult(message, data = null) {
    const timestamp = new Date().toLocaleTimeString();
    const result = {
      time: timestamp,
      message: message,
      data: data ? JSON.stringify(data, null, 2) : null
    };
    
    this.setData({
      testResults: [...this.data.testResults, result]
    });
    
    console.log(`[${timestamp}] ${message}`, data);
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  /**
   * 重新运行测试
   */
  async rerunTests() {
    this.clearResults();
    await this.runTests();
  },

  /**
   * 查看当前简历数据
   */
  viewCurrentResume() {
    const currentResume = globalResumeManager.getCurrentResume();
    this.setData({
      currentResumeData: currentResume.toObject()
    });
    
    wx.showModal({
      title: '当前简历数据',
      content: JSON.stringify(this.data.currentResumeData, null, 2),
      showCancel: false,
      confirmText: '确定'
    });
  },

  onUnload() {
    // 移除监听器
    globalResumeManager.removeListener('testPage');
  }
});
