<!--表单助手测试页面-->
<view class="test-page">
  <view class="header">
    <text class="title">表单助手测试</text>
    <text class="subtitle">测试 ResumeFormHelper 工具功能</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="rerunTests">重新运行测试</button>
    <button class="action-btn secondary" bindtap="testJobIntentionPage">测试求职意向页面</button>
  </view>

  <!-- 字段类型选择 -->
  <view class="field-selector">
    <view class="selector-header">
      <text class="selector-title">选择字段类型</text>
    </view>
    <picker range="{{fieldTypes}}" value="{{fieldTypes.indexOf(selectedFieldType)}}" bindchange="onFieldTypeChange">
      <view class="picker-display">
        <text class="picker-text">{{selectedFieldType}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 当前字段数据 -->
  <view wx:if="{{currentFieldData}}" class="data-panel">
    <view class="panel-header">
      <text class="panel-title">{{selectedFieldType}} 数据</text>
      <view class="panel-actions">
        <button class="panel-btn" bindtap="testGenerateEmptyData">生成空数据</button>
        <button class="panel-btn primary" bindtap="testSaveCurrentData">保存数据</button>
      </view>
    </view>
    <view class="data-content">
      <view wx:for="{{Object.keys(currentFieldData)}}" wx:key="*this" class="data-item">
        <text class="data-label">{{item}}:</text>
        <text class="data-value">{{currentFieldData[item] || '空'}}</text>
      </view>
    </view>
  </view>

  <!-- 功能说明 -->
  <view class="info-panel">
    <view class="panel-header">
      <text class="panel-title">功能说明</text>
    </view>
    <view class="info-content">
      <text class="info-item">🔧 getEmptyFieldData: 生成字段的空实例数据</text>
      <text class="info-item">📥 loadFieldData: 从全局管理器加载字段数据</text>
      <text class="info-item">💾 saveFieldData: 保存字段数据到全局管理器</text>
      <text class="info-item">✅ validateFieldData: 验证字段数据的有效性</text>
      <text class="info-item">🎛️ createFormPageMethods: 生成表单页面通用方法</text>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-panel">
    <view class="panel-header">
      <text class="panel-title">测试结果 ({{testResults.length}})</text>
      <button class="clear-btn" bindtap="clearResults">清空</button>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view wx:for="{{testResults}}" wx:key="index" class="result-item">
        <view class="result-header">
          <text class="result-time">{{item.time}}</text>
        </view>
        <view class="result-message">{{item.message}}</view>
      </view>
    </scroll-view>
  </view>
</view>
