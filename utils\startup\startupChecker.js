/**
 * 启动时检测工具
 * 用于在应用启动时进行各种检测
 */
const networkChecker = require('../network/networkChecker');
const apiConfig = require('../../config/apiConfig');

/**
 * 执行启动时的全面检测
 * @returns {Promise<Object>} 检测结果
 */
async function performStartupCheck() {
  console.log('开始执行启动检测...');
  
  const results = {
    network: null,
    server: null,
    overall: false,
    messages: []
  };
  
  try {
    // 检测网络连接
    const networkResult = await networkChecker.performNetworkCheck(apiConfig.baseUrl);
    results.network = networkResult;
    
    if (!networkResult.success) {
      results.messages.push(networkResult.message);
      console.warn('网络检测失败:', networkResult.message);
    } else {
      console.log('网络检测通过');
    }
    
    // 设置整体状态
    results.overall = networkResult.success;
    
  } catch (error) {
    console.error('启动检测过程中发生错误:', error);
    results.messages.push('启动检测失败');
  }
  
  console.log('启动检测完成:', results);
  return results;
}

/**
 * 显示启动检测结果
 * @param {Object} results 检测结果
 */
function showStartupCheckResult(results) {
  if (results.overall) {
    // 检测通过，不显示任何提示
    console.log('启动检测通过，应用可正常使用');
  } else {
    // 检测失败，显示警告
    const message = results.messages.length > 0 
      ? results.messages[0] 
      : '网络连接异常，部分功能可能无法使用';
      
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  }
}

/**
 * 在应用启动时执行检测
 * @param {boolean} showResult 是否显示检测结果
 * @returns {Promise<boolean>} 检测是否通过
 */
async function checkOnStartup(showResult = true) {
  try {
    const results = await performStartupCheck();
    
    if (showResult) {
      showStartupCheckResult(results);
    }
    
    return results.overall;
  } catch (error) {
    console.error('启动检测失败:', error);
    
    if (showResult) {
      wx.showToast({
        title: '网络检测失败',
        icon: 'none',
        duration: 2000
      });
    }
    
    return false;
  }
}

module.exports = {
  performStartupCheck,
  showStartupCheckResult,
  checkOnStartup
};
