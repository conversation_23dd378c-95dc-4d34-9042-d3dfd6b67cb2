/**
 * Bug修复测试页面
 * 专门测试求职意向字段更新的bug修复
 */

const app = getApp();

Page({
  data: {
    testResults: [],
    currentJobIntention: null
  },

  onLoad() {
    console.log('=== Bug修复测试页面加载 ===');
    this.runBugFixTests();
  },

  /**
   * 运行Bug修复测试
   */
  async runBugFixTests() {
    this.addTestResult('开始测试Bug修复...');
    
    await this.testJobIntentionUpdate();
    await this.testFieldTypeConversion();
    await this.testDataPersistence();
    
    this.addTestResult('✅ Bug修复测试完成');
    this.refreshJobIntentionData();
  },

  /**
   * 测试求职意向更新
   */
  async testJobIntentionUpdate() {
    this.addTestResult('\n--- 测试求职意向更新 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 测试数据
      const testJobIntention = {
        position: '前端开发工程师',
        city: '北京',
        salary: '15-20k',
        status: '在职-考虑机会'
      };
      
      this.addTestResult('📝 准备更新求职意向数据...');
      this.addTestResult(`   职位: ${testJobIntention.position}`);
      this.addTestResult(`   城市: ${testJobIntention.city}`);
      this.addTestResult(`   薪资: ${testJobIntention.salary}`);
      this.addTestResult(`   状态: ${testJobIntention.status}`);
      
      // 更新字段
      resumeManager.updateField('jobIntention', testJobIntention);
      this.addTestResult('✅ 求职意向更新成功');
      
      // 验证更新结果
      const currentResume = resumeManager.getCurrentResume();
      const jobIntention = currentResume.jobIntention;
      
      // 检查是否是正确的类实例
      if (typeof jobIntention.toObject === 'function') {
        this.addTestResult('✅ jobIntention 是正确的类实例');
      } else {
        this.addTestResult('❌ jobIntention 不是类实例');
      }
      
      // 验证数据内容
      const jobIntentionObj = jobIntention.toObject();
      if (jobIntentionObj.position === testJobIntention.position &&
          jobIntentionObj.city === testJobIntention.city &&
          jobIntentionObj.salary === testJobIntention.salary &&
          jobIntentionObj.status === testJobIntention.status) {
        this.addTestResult('✅ 求职意向数据验证通过');
      } else {
        this.addTestResult('❌ 求职意向数据验证失败');
        this.addTestResult(`   期望: ${JSON.stringify(testJobIntention)}`);
        this.addTestResult(`   实际: ${JSON.stringify(jobIntentionObj)}`);
      }
      
    } catch (error) {
      this.addTestResult('❌ 求职意向更新测试失败: ' + error.message);
      console.error('求职意向更新测试失败:', error);
    }
  },

  /**
   * 测试字段类型转换
   */
  async testFieldTypeConversion() {
    this.addTestResult('\n--- 测试字段类型转换 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 测试基本信息更新
      const testBasicInfo = {
        name: '测试用户',
        phone: '13800138000',
        email: '<EMAIL>'
      };
      
      resumeManager.updateField('basicInfo', testBasicInfo);
      this.addTestResult('✅ 基本信息更新成功');
      
      // 验证基本信息类型
      const currentResume = resumeManager.getCurrentResume();
      if (typeof currentResume.basicInfo.toObject === 'function') {
        this.addTestResult('✅ basicInfo 是正确的类实例');
      } else {
        this.addTestResult('❌ basicInfo 不是类实例');
      }
      
      // 测试数组字段更新
      const testEducation = [{
        school: '清华大学',
        major: '计算机科学与技术',
        degree: '本科',
        startDate: '2018-09',
        endDate: '2022-06'
      }];
      
      resumeManager.updateField('education', testEducation);
      this.addTestResult('✅ 教育经历更新成功');
      
      // 验证教育经历类型
      if (Array.isArray(currentResume.education) && 
          currentResume.education.length > 0 &&
          typeof currentResume.education[0].toObject === 'function') {
        this.addTestResult('✅ education 数组元素是正确的类实例');
      } else {
        this.addTestResult('❌ education 数组元素不是类实例');
      }
      
    } catch (error) {
      this.addTestResult('❌ 字段类型转换测试失败: ' + error.message);
      console.error('字段类型转换测试失败:', error);
    }
  },

  /**
   * 测试数据持久化
   */
  async testDataPersistence() {
    this.addTestResult('\n--- 测试数据持久化 ---');
    
    try {
      const resumeManager = app.getResumeManager();
      
      // 获取当前简历
      const currentResume = resumeManager.getCurrentResume();
      
      // 尝试调用 toObject 方法（这是之前出错的地方）
      const resumeObj = currentResume.toObject();
      this.addTestResult('✅ toObject() 方法调用成功');
      
      // 尝试调用 toJSON 方法
      const resumeJSON = currentResume.toJSON();
      this.addTestResult('✅ toJSON() 方法调用成功');
      this.addTestResult(`   JSON长度: ${resumeJSON.length} 字符`);
      
      // 检查本地存储
      const savedCurrentIndex = wx.getStorageSync('currentResumeIndex');
      const savedResumeMap = wx.getStorageSync('resumeDataMap');
      
      if (savedCurrentIndex && savedResumeMap) {
        this.addTestResult('✅ 本地存储保存成功');
        this.addTestResult(`   当前简历索引: ${savedCurrentIndex}`);
        this.addTestResult(`   存储的简历数量: ${Object.keys(savedResumeMap).length}`);
      } else {
        this.addTestResult('❌ 本地存储保存失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 数据持久化测试失败: ' + error.message);
      console.error('数据持久化测试失败:', error);
    }
  },

  /**
   * 刷新求职意向数据显示
   */
  refreshJobIntentionData() {
    try {
      const resumeManager = app.getResumeManager();
      const currentResume = resumeManager.getCurrentResume();
      
      if (currentResume && currentResume.jobIntention) {
        this.setData({
          currentJobIntention: currentResume.jobIntention.toObject()
        });
      }
    } catch (error) {
      console.error('刷新求职意向数据失败:', error);
    }
  },

  /**
   * 添加测试结果
   */
  addTestResult(message) {
    const timestamp = new Date().toLocaleTimeString();
    const result = {
      time: timestamp,
      message: message
    };
    
    this.setData({
      testResults: [...this.data.testResults, result]
    });
    
    console.log(`[${timestamp}] ${message}`);
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  /**
   * 重新运行测试
   */
  async rerunTests() {
    this.clearResults();
    await this.runBugFixTests();
  },

  /**
   * 测试求职意向页面
   */
  testJobIntentionPage() {
    wx.navigateTo({
      url: '/pages/makeResume/jobIntention/jobIntention'
    });
  },

  /**
   * 手动触发求职意向更新
   */
  manualUpdateJobIntention() {
    try {
      const resumeManager = app.getResumeManager();
      
      const testData = {
        position: '手动测试职位',
        city: '手动测试城市',
        salary: '面议',
        status: '手动测试状态'
      };
      
      resumeManager.updateField('jobIntention', testData);
      
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });
      
      this.addTestResult('✅ 手动更新求职意向成功');
      this.refreshJobIntentionData();
      
    } catch (error) {
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
      
      this.addTestResult('❌ 手动更新求职意向失败: ' + error.message);
    }
  }
});
