/* 简历数据测试页面样式 */

.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
}

.status-text.success {
  background-color: #e8f5e8;
  color: #52c41a;
}

.status-text.error {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background-color: #1890ff;
  color: white;
}

.action-btn.secondary {
  background-color: white;
  color: #666;
  border: 2rpx solid #d9d9d9;
}

.results-container {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

.results-header {
  background-color: #fafafa;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.results-list {
  max-height: 800rpx;
  padding: 20rpx;
}

.result-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  border-left: 6rpx solid #1890ff;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-message {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
  margin-bottom: 10rpx;
}

.result-data {
  background-color: #f6f8fa;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.data-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.data-content {
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

.info-panel {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
