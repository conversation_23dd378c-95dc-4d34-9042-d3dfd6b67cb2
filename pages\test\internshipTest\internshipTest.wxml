<!--实习经历模块测试页面-->
<view class="test-page">
  <view class="header">
    <text class="title">实习经历模块测试</text>
    <text class="subtitle">测试改造后的实习经历功能</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="rerunTests">重新运行测试</button>
    <button class="action-btn secondary" bindtap="testInternshipListPage">测试列表页面</button>
  </view>

  <view class="actions">
    <button class="action-btn info" bindtap="testInternshipEditPageAdd">测试新增页面</button>
    <button class="action-btn info" bindtap="testInternshipEditPageEdit">测试编辑页面</button>
  </view>

  <!-- 当前实习经历列表 -->
  <view class="data-panel">
    <view class="panel-header">
      <text class="panel-title">当前实习经历 ({{currentInternshipList.length}})</text>
      <view class="panel-actions">
        <button class="panel-btn" bindtap="addTestData">添加测试数据</button>
        <button class="panel-btn danger" bindtap="clearAllInternships">清空全部</button>
      </view>
    </view>
    
    <view wx:if="{{currentInternshipList.length > 0}}" class="internship-list">
      <view wx:for="{{currentInternshipList}}" wx:key="index" class="internship-item">
        <view class="internship-header">
          <text class="company-name">{{item.company}}</text>
          <text class="position-name">{{item.position}}</text>
        </view>
        <view class="internship-details">
          <text class="date-range">{{item.startDate}} - {{item.endDate}}</text>
          <text class="content" wx:if="{{item.content}}">{{item.content}}</text>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-state">
      <text class="empty-text">暂无实习经历数据</text>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-panel">
    <view class="panel-header">
      <text class="panel-title">测试结果 ({{testResults.length}})</text>
      <button class="clear-btn" bindtap="clearResults">清空</button>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view wx:for="{{testResults}}" wx:key="index" class="result-item">
        <view class="result-header">
          <text class="result-time">{{item.time}}</text>
        </view>
        <view class="result-message">{{item.message}}</view>
      </view>
    </scroll-view>
  </view>

  <!-- 测试说明 -->
  <view class="info-panel">
    <view class="panel-header">
      <text class="panel-title">改造说明</text>
    </view>
    <view class="info-content">
      <text class="info-item">🔧 取消硬编码: 使用 InternshipItem 类的空实例</text>
      <text class="info-item">📦 统一管理: 使用 ResumeFormHelper 工具</text>
      <text class="info-item">🗑️ 移除旧代码: 删除 resumeManager 相关代码</text>
      <text class="info-item">✅ 数据验证: 内置验证机制</text>
      <text class="info-item">🔄 自动保存: 数据变更自动同步</text>
    </view>
  </view>
</view>
