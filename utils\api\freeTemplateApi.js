/**
 * 免费简历模板API接口
 */
const request = require('./request');
const apiConfig = require('../../config/apiConfig');

/**
 * 获取所有免费模板列表
 * @param {Object} params 查询参数
 * @param {number} params.skip 跳过的记录数，默认为0
 * @param {number} params.limit 返回的记录数，默认为20，最大1000
 * @param {string} params.batch_flag 批次标识筛选 (可选)
 * @param {string} params.type 文件类型筛选 (可选)
 * @returns {Promise<Object>} 模板列表响应
 */
function getFreeTemplateList(params = {}) {
  const queryParams = {
    skip: params.skip || 0,
    limit: params.limit || 20,
    batch_flag: params.batch_flag || '',
    type: params.type || ''
  };

  // 构建查询字符串，过滤空值
  const queryString = Object.keys(queryParams)
    .filter(key => queryParams[key] !== '')
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  console.log('=== 获取免费模板列表API请求 ===');
  console.log('查询参数:', queryParams);

  return request.request({
    url: `/free-templates/${queryString ? '?' + queryString : ''}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 免费模板列表不需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取批次列表
 * @returns {Promise<Array>} 批次标识列表
 */
function getBatches() {
  console.log('=== 获取批次列表API请求 ===');

  return request.request({
    url: '/free-templates/batches',
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 根据批次获取模板
 * @param {string} batchFlag 批次标识
 * @param {Object} params 查询参数
 * @param {number} params.skip 跳过的记录数，默认为0
 * @param {number} params.limit 返回的记录数，默认为20
 * @returns {Promise<Object>} 批次模板响应
 */
function getTemplatesByBatch(batchFlag, params = {}) {
  if (!batchFlag) {
    return Promise.reject(new Error('批次标识不能为空'));
  }

  const queryParams = {
    skip: params.skip || 0,
    limit: params.limit || 20
  };

  const queryString = Object.keys(queryParams)
    .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  console.log('=== 根据批次获取模板API请求 ===');
  console.log('批次标识:', batchFlag);
  console.log('查询参数:', queryParams);

  return request.request({
    url: `/free-templates/batch/${encodeURIComponent(batchFlag)}?${queryString}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取模板下载链接 ⭐
 * @param {string} templateId 模板ID（会自动进行URL编码）
 * @returns {Promise<Object>} 下载链接响应
 */
function getDownloadLinks(templateId) {
  if (!templateId) {
    return Promise.reject(new Error('模板ID不能为空'));
  }

  // URL编码模板ID，处理斜杠等特殊字符
  const encodedTemplateId = encodeURIComponent(templateId);

  console.log('=== 获取下载链接API请求 ===');
  console.log('原始模板ID:', templateId);
  console.log('编码后模板ID:', encodedTemplateId);

  return request.request({
    url: `/free-templates/${encodedTemplateId}/download`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false, // 下载链接不需要认证
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取模板详情
 * @param {string} templateId 模板ID（会自动进行URL编码）
 * @returns {Promise<Object>} 模板详情响应
 */
function getTemplateDetail(templateId) {
  if (!templateId) {
    return Promise.reject(new Error('模板ID不能为空'));
  }

  const encodedTemplateId = encodeURIComponent(templateId);

  console.log('=== 获取模板详情API请求 ===');
  console.log('原始模板ID:', templateId);
  console.log('编码后模板ID:', encodedTemplateId);

  return request.request({
    url: `/free-templates/${encodedTemplateId}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

/**
 * 获取统计信息
 * @returns {Promise<Object>} 统计信息响应
 */
function getStats() {
  console.log('=== 获取统计信息API请求 ===');

  return request.request({
    url: '/free-templates/stats/summary',
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    },
    showLoading: false,
    showError: false,
    needAuth: false,
    timeout: apiConfig.timeout?.default || 5000
  });
}

module.exports = {
  getFreeTemplateList,
  getBatches,
  getTemplatesByBatch,
  getDownloadLinks,
  getTemplateDetail,
  getStats
};
