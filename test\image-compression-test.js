/**
 * 图片压缩功能测试
 * 用于验证图片压缩工具的效果
 */

// 模拟微信小程序环境
const mockWx = {
  nextTick: (callback) => setTimeout(callback, 0),
  env: {
    USER_DATA_PATH: '/mock/path'
  },
  getFileSystemManager: () => ({
    writeFileSync: () => {},
    readdir: () => {},
    unlink: () => {},
    readFile: (options) => {
      // 模拟读取文件返回base64数据
      const mockBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      options.success({ data: mockBase64 });
    }
  }),
  getImageInfo: (options) => {
    // 模拟获取图片信息
    setTimeout(() => {
      options.success({
        width: 800,
        height: 1200,
        path: options.src
      });
    }, 10);
  },
  createCanvasContext: () => ({
    drawImage: () => {},
    draw: (callback) => {
      if (callback) setTimeout(callback, 10);
    }
  }),
  canvasToTempFilePath: (options) => {
    // 模拟canvas导出
    setTimeout(() => {
      options.success({
        tempFilePath: '/mock/compressed/image.jpg'
      });
    }, 50);
  }
};

// 设置全局wx对象
global.wx = mockWx;

// 引入图片压缩工具和性能监控
const imageCompressor = require('../utils/imageCompressor');
const performanceMonitor = require('../utils/performance/performanceMonitor');

// 测试数据
const testCases = [
  {
    name: '高分辨率图片',
    width: 2000,
    height: 3000,
    expectedCompression: 'strong'
  },
  {
    name: '中等分辨率图片',
    width: 1200,
    height: 1800,
    expectedCompression: 'medium'
  },
  {
    name: '低分辨率图片',
    width: 600,
    height: 900,
    expectedCompression: 'light'
  }
];

// 测试压缩建议功能
function testCompressionSuggestions() {
  console.log('========== 测试压缩建议功能 ==========');
  
  testCases.forEach(testCase => {
    const suggestion = imageCompressor.getCompressionSuggestion(
      testCase.width, 
      testCase.height
    );
    
    console.log(`${testCase.name} (${testCase.width}x${testCase.height}):`);
    console.log('  建议配置:', suggestion);
    console.log('  压缩原因:', suggestion.reason);
    console.log('');
  });
}

// 测试尺寸计算功能
function testSizeCalculation() {
  console.log('========== 测试尺寸计算功能 ==========');
  
  const testSizes = [
    { original: [2000, 3000], max: [400, 600] },
    { original: [1200, 1800], max: [400, 600] },
    { original: [300, 450], max: [400, 600] }
  ];
  
  testSizes.forEach(test => {
    const result = imageCompressor.calculateCompressedSize(
      test.original[0], 
      test.original[1], 
      test.max[0], 
      test.max[1]
    );
    
    console.log(`原始尺寸: ${test.original[0]}x${test.original[1]}`);
    console.log(`最大尺寸: ${test.max[0]}x${test.max[1]}`);
    console.log(`压缩后尺寸: ${result.width}x${result.height}`);
    console.log('');
  });
}

// 测试大小估算功能
function testSizeEstimation() {
  console.log('========== 测试大小估算功能 ==========');
  
  const testImages = [
    { width: 800, height: 1200, name: '证件照尺寸' },
    { width: 1200, height: 1800, name: '高清证件照' },
    { width: 2000, height: 3000, name: '超高清图片' }
  ];
  
  testImages.forEach(img => {
    const estimatedSize = imageCompressor.estimateOriginalSize(img.width, img.height);
    console.log(`${img.name} (${img.width}x${img.height}):`);
    console.log(`  预估大小: ${imageCompressor.formatSize(estimatedSize)}`);
    console.log('');
  });
}

// 测试base64大小计算
function testBase64SizeCalculation() {
  console.log('========== 测试Base64大小计算 ==========');
  
  const testBase64Strings = [
    'data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    ''
  ];
  
  testBase64Strings.forEach((base64, index) => {
    const size = imageCompressor.getBase64Size(base64);
    console.log(`测试字符串 ${index + 1}:`);
    console.log(`  长度: ${base64.length}`);
    console.log(`  计算大小: ${imageCompressor.formatSize(size)}`);
    console.log('');
  });
}

// 模拟图片压缩流程测试
async function testImageCompressionFlow() {
  console.log('========== 测试图片压缩流程 ==========');
  
  // 重置性能监控
  performanceMonitor.reset();
  
  try {
    // 模拟压缩一张图片
    console.log('开始压缩测试图片...');
    
    const mockFilePath = '/mock/test/image.jpg';
    const compressedBase64 = await imageCompressor.compressImage(mockFilePath, {
      quality: 0.8,
      maxWidth: 400,
      maxHeight: 600,
      format: 'jpeg'
    });
    
    console.log('压缩完成!');
    console.log('压缩后的base64长度:', compressedBase64.length);
    
    // 打印性能报告
    console.log('\n压缩性能报告:');
    performanceMonitor.printReport();
    
  } catch (error) {
    console.error('压缩测试失败:', error);
  }
}

// 测试压缩配置优化
function testCompressionOptimization() {
  console.log('========== 测试压缩配置优化 ==========');
  
  const scenarios = [
    { width: 3000, height: 4000, desc: '超高分辨率场景' },
    { width: 1500, height: 2000, desc: '高分辨率场景' },
    { width: 800, height: 1200, desc: '标准分辨率场景' },
    { width: 400, height: 600, desc: '低分辨率场景' }
  ];
  
  scenarios.forEach(scenario => {
    const suggestion = imageCompressor.getCompressionSuggestion(
      scenario.width, 
      scenario.height
    );
    
    const originalSize = imageCompressor.estimateOriginalSize(
      scenario.width, 
      scenario.height
    );
    
    const compressedSize = imageCompressor.estimateOriginalSize(
      suggestion.maxWidth, 
      suggestion.maxHeight
    ) * suggestion.quality;
    
    const savings = originalSize - compressedSize;
    const savingsPercent = (savings / originalSize * 100).toFixed(1);
    
    console.log(`${scenario.desc}:`);
    console.log(`  原始尺寸: ${scenario.width}x${scenario.height}`);
    console.log(`  建议尺寸: ${suggestion.maxWidth}x${suggestion.maxHeight}`);
    console.log(`  建议质量: ${suggestion.quality}`);
    console.log(`  预估原始大小: ${imageCompressor.formatSize(originalSize)}`);
    console.log(`  预估压缩后大小: ${imageCompressor.formatSize(compressedSize)}`);
    console.log(`  预估节省空间: ${imageCompressor.formatSize(savings)} (${savingsPercent}%)`);
    console.log(`  压缩原因: ${suggestion.reason}`);
    console.log('');
  });
}

// 运行所有测试
async function runAllTests() {
  console.log('开始图片压缩功能测试...\n');
  
  testCompressionSuggestions();
  testSizeCalculation();
  testSizeEstimation();
  testBase64SizeCalculation();
  testCompressionOptimization();
  
  // 异步测试
  await testImageCompressionFlow();
  
  console.log('\n所有测试完成!');
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testCompressionSuggestions,
  testSizeCalculation,
  testSizeEstimation,
  testBase64SizeCalculation,
  testImageCompressionFlow,
  testCompressionOptimization,
  runAllTests
};
