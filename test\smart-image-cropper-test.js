/**
 * 智能图片裁剪工具测试
 * 测试各种裁剪场景和算法
 */

// 模拟微信小程序环境
global.wx = {
  getImageInfo: (options) => {
    console.log('模拟 wx.getImageInfo 调用:', options.src);
    // 模拟不同尺寸的图片
    const mockImages = {
      '/mock/portrait.jpg': { width: 600, height: 800, path: '/mock/portrait.jpg', type: 'jpeg' },
      '/mock/landscape.jpg': { width: 1200, height: 800, path: '/mock/landscape.jpg', type: 'jpeg' },
      '/mock/square.jpg': { width: 800, height: 800, path: '/mock/square.jpg', type: 'jpeg' },
      '/mock/wide.jpg': { width: 1600, height: 600, path: '/mock/wide.jpg', type: 'jpeg' },
      '/mock/tall.jpg': { width: 400, height: 1200, path: '/mock/tall.jpg', type: 'jpeg' }
    };
    
    const imageInfo = mockImages[options.src] || { width: 800, height: 600, path: options.src, type: 'jpeg' };
    setTimeout(() => options.success(imageInfo), 10);
  },
  
  createCanvasContext: (canvasId) => {
    console.log('模拟创建Canvas上下文:', canvasId);
    return {
      clearRect: (x, y, w, h) => console.log(`清空Canvas: ${x},${y},${w},${h}`),
      drawImage: (...args) => console.log('绘制图片到Canvas:', args),
      draw: (reserve, callback) => {
        console.log('Canvas绘制完成');
        setTimeout(callback, 10);
      }
    };
  },
  
  canvasToTempFilePath: (options) => {
    console.log('导出Canvas为临时文件:', options);
    setTimeout(() => {
      options.success({ tempFilePath: '/mock/cropped_' + Date.now() + '.jpg' });
    }, 20);
  },
  
  getFileSystemManager: () => ({
    readFile: (options) => {
      console.log('读取文件为base64:', options.filePath);
      // 模拟base64数据
      const mockBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      setTimeout(() => {
        options.success({ data: mockBase64 });
      }, 10);
    }
  })
};

// 引入智能裁剪工具
const smartImageCropper = require('../utils/smartImageCropper');

// 测试用例
async function runTests() {
  console.log('========== 智能图片裁剪工具测试 ==========\n');
  
  // 测试1: 计算裁剪参数
  console.log('1. 测试裁剪参数计算');
  testCropParamsCalculation();
  
  // 测试2: 推荐裁剪模式
  console.log('\n2. 测试推荐裁剪模式');
  testRecommendedCropMode();
  
  // 测试3: 智能裁剪流程
  console.log('\n3. 测试智能裁剪流程');
  await testSmartCropFlow();
  
  // 测试4: 批量处理
  console.log('\n4. 测试批量处理');
  await testBatchProcess();
  
  console.log('\n========== 测试完成 ==========');
}

// 测试裁剪参数计算
function testCropParamsCalculation() {
  const testCases = [
    {
      name: '横向图片 (1200x800) -> 120x150',
      imageInfo: { width: 1200, height: 800 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    },
    {
      name: '竖向图片 (600x800) -> 120x150',
      imageInfo: { width: 600, height: 800 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    },
    {
      name: '正方形图片 (800x800) -> 120x150',
      imageInfo: { width: 800, height: 800 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n测试: ${testCase.name}`);
    const cropParams = smartImageCropper.calculateCropParams(testCase.imageInfo, testCase.config);
    console.log('裁剪参数:', cropParams);
    
    // 验证裁剪参数的合理性
    const { cropX, cropY, cropWidth, cropHeight, outputWidth, outputHeight } = cropParams;
    const isValid = 
      cropX >= 0 && 
      cropY >= 0 && 
      cropX + cropWidth <= testCase.imageInfo.width &&
      cropY + cropHeight <= testCase.imageInfo.height &&
      outputWidth === testCase.config.targetWidth &&
      outputHeight === testCase.config.targetHeight;
    
    console.log('参数验证:', isValid ? '✓ 通过' : '✗ 失败');
  });
}

// 测试推荐裁剪模式
function testRecommendedCropMode() {
  const testImages = [
    { name: '竖向人像', width: 600, height: 800 },
    { name: '横向风景', width: 1600, height: 600 },
    { name: '正方形', width: 800, height: 800 },
    { name: '超宽图片', width: 2000, height: 600 },
    { name: '超高图片', width: 400, height: 1200 }
  ];
  
  testImages.forEach(image => {
    const mode = smartImageCropper.getRecommendedCropMode(image);
    console.log(`${image.name} (${image.width}x${image.height}): 推荐模式 = ${mode}`);
  });
}

// 测试智能裁剪流程
async function testSmartCropFlow() {
  const testImages = [
    '/mock/portrait.jpg',
    '/mock/landscape.jpg',
    '/mock/square.jpg'
  ];
  
  for (const imagePath of testImages) {
    try {
      console.log(`\n测试裁剪: ${imagePath}`);
      
      const result = await smartImageCropper.smartCrop(imagePath, {
        targetWidth: 120,
        targetHeight: 150,
        quality: 0.8,
        format: 'jpeg',
        cropMode: 'center'
      });
      
      console.log('裁剪成功 ✓');
      console.log('结果类型:', typeof result);
      console.log('是否为base64:', result.startsWith('data:image/'));
      
    } catch (error) {
      console.log('裁剪失败 ✗:', error.message);
    }
  }
}

// 测试批量处理
async function testBatchProcess() {
  const imagePaths = [
    '/mock/portrait.jpg',
    '/mock/landscape.jpg',
    '/mock/square.jpg',
    '/mock/invalid.jpg'  // 故意添加一个无效路径
  ];
  
  try {
    console.log('开始批量处理...');
    
    const results = await smartImageCropper.batchProcess(imagePaths, {
      targetWidth: 120,
      targetHeight: 150,
      quality: 0.8,
      format: 'jpeg'
    });
    
    console.log('\n批量处理结果:');
    results.forEach((result, index) => {
      const status = result.success ? '✓ 成功' : '✗ 失败';
      console.log(`${imagePaths[index]}: ${status}`);
      if (!result.success) {
        console.log(`  错误: ${result.error}`);
      }
    });
    
    const successCount = results.filter(r => r.success).length;
    console.log(`\n成功: ${successCount}/${results.length}`);
    
  } catch (error) {
    console.log('批量处理失败:', error.message);
  }
}

// 性能测试
function performanceTest() {
  console.log('\n========== 性能测试 ==========');
  
  const iterations = 100;
  const testImage = { width: 1200, height: 800 };
  const config = { targetWidth: 120, targetHeight: 150, cropMode: 'center' };
  
  console.log(`执行 ${iterations} 次裁剪参数计算...`);
  
  const startTime = Date.now();
  
  for (let i = 0; i < iterations; i++) {
    smartImageCropper.calculateCropParams(testImage, config);
  }
  
  const endTime = Date.now();
  const avgTime = (endTime - startTime) / iterations;
  
  console.log(`平均耗时: ${avgTime.toFixed(2)}ms`);
  console.log(`总耗时: ${endTime - startTime}ms`);
}

// 边界条件测试
function boundaryTest() {
  console.log('\n========== 边界条件测试 ==========');
  
  const edgeCases = [
    {
      name: '极小图片',
      imageInfo: { width: 10, height: 10 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    },
    {
      name: '极大图片',
      imageInfo: { width: 10000, height: 8000 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    },
    {
      name: '极窄图片',
      imageInfo: { width: 100, height: 2000 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    },
    {
      name: '极宽图片',
      imageInfo: { width: 3000, height: 100 },
      config: { targetWidth: 120, targetHeight: 150, cropMode: 'center' }
    }
  ];
  
  edgeCases.forEach(testCase => {
    console.log(`\n测试: ${testCase.name}`);
    try {
      const cropParams = smartImageCropper.calculateCropParams(testCase.imageInfo, testCase.config);
      console.log('计算成功 ✓');
      console.log('裁剪区域:', `${cropParams.cropWidth}x${cropParams.cropHeight}`);
    } catch (error) {
      console.log('计算失败 ✗:', error.message);
    }
  });
}

// 运行所有测试
async function runAllTests() {
  await runTests();
  performanceTest();
  boundaryTest();
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  runTests,
  performanceTest,
  boundaryTest,
  runAllTests
};
