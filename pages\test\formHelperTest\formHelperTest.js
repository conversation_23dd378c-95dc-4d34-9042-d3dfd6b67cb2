/**
 * 表单助手测试页面
 * 测试 ResumeFormHelper 工具的功能
 */

const app = getApp();
const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');

Page({
  data: {
    testResults: [],
    fieldTypes: [
      'basicInfo',
      'jobIntention', 
      'educationItem',
      'workItem',
      'projectItem',
      'customItem',
      'schoolExperienceItem',
      'internshipItem'
    ],
    currentFieldData: null,
    selectedFieldType: 'jobIntention'
  },

  onLoad() {
    console.log('=== 表单助手测试页面加载 ===');
    this.runFormHelperTests();
  },

  /**
   * 运行表单助手测试
   */
  async runFormHelperTests() {
    this.addTestResult('开始测试表单助手功能...');
    
    await this.testEmptyFieldData();
    await this.testLoadFieldData();
    await this.testSaveFieldData();
    await this.testValidateFieldData();
    await this.testFormPageMethods();
    
    this.addTestResult('✅ 表单助手测试完成');
    this.loadCurrentFieldData();
  },

  /**
   * 测试空字段数据生成
   */
  async testEmptyFieldData() {
    this.addTestResult('\n--- 测试空字段数据生成 ---');
    
    try {
      for (const fieldType of this.data.fieldTypes) {
        const emptyData = ResumeFormHelper.getEmptyFieldData(fieldType);
        
        if (emptyData && typeof emptyData === 'object') {
          this.addTestResult(`✅ ${fieldType}: 生成成功`);
          this.addTestResult(`   字段数量: ${Object.keys(emptyData).length}`);
        } else {
          this.addTestResult(`❌ ${fieldType}: 生成失败`);
        }
      }
    } catch (error) {
      this.addTestResult('❌ 空字段数据生成测试失败: ' + error.message);
    }
  },

  /**
   * 测试加载字段数据
   */
  async testLoadFieldData() {
    this.addTestResult('\n--- 测试加载字段数据 ---');
    
    try {
      // 测试加载求职意向数据
      const jobIntentionData = ResumeFormHelper.loadFieldData('jobIntention', app);
      this.addTestResult('✅ 求职意向数据加载成功');
      this.addTestResult(`   数据内容: ${JSON.stringify(jobIntentionData)}`);
      
      // 测试加载基本信息数据
      const basicInfoData = ResumeFormHelper.loadFieldData('basicInfo', app);
      this.addTestResult('✅ 基本信息数据加载成功');
      this.addTestResult(`   姓名: ${basicInfoData.name || '未填写'}`);
      
    } catch (error) {
      this.addTestResult('❌ 加载字段数据测试失败: ' + error.message);
    }
  },

  /**
   * 测试保存字段数据
   */
  async testSaveFieldData() {
    this.addTestResult('\n--- 测试保存字段数据 ---');
    
    try {
      // 测试保存求职意向数据
      const testJobIntention = {
        position: '测试职位',
        city: '测试城市',
        salary: '测试薪资',
        status: '测试状态'
      };
      
      const saveSuccess = ResumeFormHelper.saveFieldData('jobIntention', testJobIntention, app);
      
      if (saveSuccess) {
        this.addTestResult('✅ 求职意向数据保存成功');
        
        // 验证保存结果
        const loadedData = ResumeFormHelper.loadFieldData('jobIntention', app);
        if (loadedData.position === testJobIntention.position) {
          this.addTestResult('✅ 保存数据验证通过');
        } else {
          this.addTestResult('❌ 保存数据验证失败');
        }
      } else {
        this.addTestResult('❌ 求职意向数据保存失败');
      }
      
    } catch (error) {
      this.addTestResult('❌ 保存字段数据测试失败: ' + error.message);
    }
  },

  /**
   * 测试验证字段数据
   */
  async testValidateFieldData() {
    this.addTestResult('\n--- 测试验证字段数据 ---');
    
    try {
      // 测试有效数据验证
      const validData = {
        position: '前端开发工程师',
        city: '北京',
        salary: '10-15k',
        status: '在职'
      };
      
      const validErrors = ResumeFormHelper.validateFieldData('jobIntention', validData);
      this.addTestResult(`✅ 有效数据验证: ${validErrors.length} 个错误`);
      
      // 测试无效数据验证
      const invalidData = {
        position: '',
        city: '',
        salary: '',
        status: ''
      };
      
      const invalidErrors = ResumeFormHelper.validateFieldData('jobIntention', invalidData);
      this.addTestResult(`📝 空数据验证: ${invalidErrors.length} 个错误`);
      
    } catch (error) {
      this.addTestResult('❌ 验证字段数据测试失败: ' + error.message);
    }
  },

  /**
   * 测试表单页面方法
   */
  async testFormPageMethods() {
    this.addTestResult('\n--- 测试表单页面方法 ---');
    
    try {
      const methods = ResumeFormHelper.createFormPageMethods('jobIntention');
      
      // 检查方法是否存在
      const expectedMethods = ['loadData', 'saveData', 'deleteData', 'validateData'];
      let allMethodsExist = true;
      
      for (const methodName of expectedMethods) {
        if (typeof methods[methodName] === 'function') {
          this.addTestResult(`✅ ${methodName} 方法存在`);
        } else {
          this.addTestResult(`❌ ${methodName} 方法不存在`);
          allMethodsExist = false;
        }
      }
      
      if (allMethodsExist) {
        this.addTestResult('✅ 所有表单页面方法生成成功');
      } else {
        this.addTestResult('❌ 部分表单页面方法缺失');
      }
      
    } catch (error) {
      this.addTestResult('❌ 表单页面方法测试失败: ' + error.message);
    }
  },

  /**
   * 加载当前字段数据
   */
  loadCurrentFieldData() {
    try {
      const fieldData = ResumeFormHelper.loadFieldData(this.data.selectedFieldType, app);
      this.setData({
        currentFieldData: fieldData
      });
    } catch (error) {
      console.error('加载当前字段数据失败:', error);
    }
  },

  /**
   * 字段类型选择变化
   */
  onFieldTypeChange(e) {
    const selectedIndex = e.detail.value;
    const selectedFieldType = this.data.fieldTypes[selectedIndex];
    
    this.setData({
      selectedFieldType: selectedFieldType
    });
    
    this.loadCurrentFieldData();
    this.addTestResult(`🔄 切换到字段类型: ${selectedFieldType}`);
  },

  /**
   * 测试生成空数据
   */
  testGenerateEmptyData() {
    try {
      const emptyData = ResumeFormHelper.getEmptyFieldData(this.data.selectedFieldType);
      
      this.setData({
        currentFieldData: emptyData
      });
      
      this.addTestResult(`✅ 生成 ${this.data.selectedFieldType} 空数据成功`);
      
      wx.showToast({
        title: '生成成功',
        icon: 'success'
      });
      
    } catch (error) {
      this.addTestResult(`❌ 生成 ${this.data.selectedFieldType} 空数据失败: ${error.message}`);
      
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 测试保存当前数据
   */
  testSaveCurrentData() {
    try {
      const success = ResumeFormHelper.saveFieldData(
        this.data.selectedFieldType, 
        this.data.currentFieldData, 
        app
      );
      
      if (success) {
        this.addTestResult(`✅ 保存 ${this.data.selectedFieldType} 数据成功`);
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } else {
        this.addTestResult(`❌ 保存 ${this.data.selectedFieldType} 数据失败`);
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      this.addTestResult(`❌ 保存 ${this.data.selectedFieldType} 数据失败: ${error.message}`);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 测试求职意向页面
   */
  testJobIntentionPage() {
    wx.navigateTo({
      url: '/pages/makeResume/jobIntention/jobIntention'
    });
  },

  /**
   * 添加测试结果
   */
  addTestResult(message) {
    const timestamp = new Date().toLocaleTimeString();
    const result = {
      time: timestamp,
      message: message
    };
    
    this.setData({
      testResults: [...this.data.testResults, result]
    });
    
    console.log(`[${timestamp}] ${message}`);
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({
      testResults: []
    });
  },

  /**
   * 重新运行测试
   */
  async rerunTests() {
    this.clearResults();
    await this.runFormHelperTests();
  }
});
