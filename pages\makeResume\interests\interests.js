const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    interestsList: null  // 将在 onLoad 中初始化
  },

  onLoad() {
    console.log('=== 兴趣爱好页面加载 ===');
    this.loadInterestsData();
  },

  /**
   * 从全局管理器加载兴趣爱好数据
   */
  loadInterestsData() {
    try {
      const interestsData = ResumeFormHelper.loadFieldData('interests', app);

      // interestsData 现在是一个包含16个元素的数组（包括空字符串）
      this.setData({
        interestsList: interestsData
      });

      console.log('✅ 兴趣爱好数据加载成功:', interestsData);
    } catch (error) {
      console.error('❌ 加载兴趣爱好数据失败:', error);
      // 出错时使用空数据
      const emptyData = ResumeFormHelper.getEmptyFieldData('interests');
      this.setData({
        interestsList: emptyData
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;

    const interestsList = [...this.data.interestsList];
    interestsList[index] = value;

    this.setData({
      interestsList: interestsList
    });
  },

  /**
   * 保存兴趣爱好信息
   */
  saveInfo() {
    try {
      // 过滤掉空字符串，并确保每个项都是字符串
      const interestsList = this.data.interestsList
        .map(item => {
          if (typeof item === 'object' && item !== null) {
            return item.content || '';
          }
          return item;
        })
        .filter(item => typeof item === 'string' && item.trim() !== '');

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('interests', interestsList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 兴趣爱好保存成功:', interestsList);

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 兴趣爱好保存失败');
      }
    } catch (error) {
      console.error('❌ 保存兴趣爱好时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除所有兴趣爱好信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有兴趣爱好吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用 ResumeFormHelper 清空数据
            const success = ResumeFormHelper.clearFieldData('interests', app);

            if (success) {
              // 更新页面显示
              const emptyData = ResumeFormHelper.getEmptyFieldData('interests');
              this.setData({
                interestsList: emptyData
              });

              wx.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });

              console.log('✅ 兴趣爱好删除成功');

              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
              console.error('❌ 兴趣爱好删除失败');
            }
          } catch (error) {
            console.error('❌ 删除兴趣爱好时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});