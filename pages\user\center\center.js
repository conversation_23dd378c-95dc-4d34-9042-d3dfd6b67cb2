// pages/user/center/center.js
const membershipManager = require('../../../utils/user/membershipManager');

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    isMember: false,
    membershipStatus: '普通用户', // 会员状态文本
    membershipInfo: null,
    membershipExpiry: null,
    isLoading: false
  },

  onLoad() {
    // 检查登录状态
    this.checkLoginStatus();
  },

  onShow() {
    // 每次显示页面时检查登录状态并刷新数据
    this.checkLoginStatus();
    this.updateMembershipStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('userToken');
    const userId = wx.getStorageSync('userId');

    if (token && userId) {
      this.setData({
        hasUserInfo: true,
        userInfo: { nickName: '微信用户', avatarUrl: '/pages/index/images/touXiang.png' }
      });
      return true;
    } else {
      this.setData({
        hasUserInfo: false,
        userInfo: null
      });
      return false;
    }
  },

  // 更新会员状态
  updateMembershipStatus() {
    console.log('更新会员状态...');

    // 使用会员管理工具查询最新状态
    membershipManager.queryMemberStatus(false)
      .then((membershipInfo) => {
        console.log('会员状态查询成功:', membershipInfo);

        const isMember = membershipInfo.isMember || false;
        const membershipStatus = membershipInfo.message || (isMember ? '会员用户' : '普通用户');

        this.setData({
          isMember: isMember,
          membershipStatus: membershipStatus,
          membershipInfo: membershipInfo
        });
      })
      .catch((err) => {
        console.error('会员状态查询失败:', err);

        // 查询失败时使用缓存或默认状态
        const cachedInfo = membershipManager.getCachedMemberInfo();
        if (cachedInfo) {
          this.setData({
            isMember: cachedInfo.isMember || false,
            membershipStatus: cachedInfo.message || '普通用户',
            membershipInfo: cachedInfo
          });
        } else {
          this.setData({
            isMember: false,
            membershipStatus: '普通用户',
            membershipInfo: null
          });
        }
      });
  },





  // 升级会员
  upgradeMembership() {
    wx.showModal({
      title: '升级会员',
      content: '升级会员可享受更多功能和特权，是否前往升级？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到会员升级页面或调用支付接口
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 刷新会员状态
  refreshMemberStatus() {
    console.log('手动刷新会员状态...');

    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 强制刷新会员状态
    membershipManager.queryMemberStatus(true)
      .then((membershipInfo) => {
        console.log('会员状态刷新成功:', membershipInfo);

        const isMember = membershipInfo.isMember || false;
        const membershipStatus = membershipInfo.message || (isMember ? '会员用户' : '普通用户');

        this.setData({
          isMember: isMember,
          membershipStatus: membershipStatus,
          membershipInfo: membershipInfo
        });

        wx.hideLoading();
        wx.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 2000
        });
      })
      .catch((err) => {
        console.error('会员状态刷新失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '刷新失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 跳转到我的简历页面
  goToMyResumes() {
    if (!this.checkLoginStatus()) {
      this.redirectToLogin('/pages/user/resumes/resumes');
      return;
    }

    wx.navigateTo({
      url: '/pages/user/resumes/resumes'
    });
  },



  // 跳转到账号设置页面
  goToSettings() {
    if (!this.checkLoginStatus()) {
      this.redirectToLogin('/pages/user/settings/settings');
      return;
    }

    wx.navigateTo({
      url: '/pages/user/settings/settings'
    });
  },

  // 重定向到登录页面
  redirectToLogin(redirectUrl) {
    wx.navigateTo({
      url: `/pages/user/login/login?redirect=${encodeURIComponent(redirectUrl)}`
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {


          // 清除本地存储的登录信息
          wx.removeStorageSync('userToken');
          wx.removeStorageSync('userInfo');
          wx.removeStorageSync('userId');

          // 清除会员信息
          membershipManager.clearMemberInfo();

          // 更新状态
          this.setData({
            hasUserInfo: false,
            userInfo: null
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },


})
