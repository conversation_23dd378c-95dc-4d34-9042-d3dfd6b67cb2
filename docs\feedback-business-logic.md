# Feedback功能业务逻辑文档

## 功能概述

Feedback功能允许用户在使用小程序过程中提交反馈信息，包括功能建议、问题反馈、表扬鼓励等，帮助开发者收集用户意见并改进产品。

## 前端实现

### 1. 自定义导航栏组件
- **位置**: `components/customNavBar/customNavBar.js`
- **功能**: 在页面顶部显示分享和反馈按钮
- **触发方式**: 用户点击反馈按钮（💬图标）

### 2. 反馈页面
- **位置**: `pages/feedback/feedback.js`
- **路由**: `/pages/feedback/feedback`
- **功能**: 提供反馈表单界面

### 3. 反馈类型
```javascript
const typeOptions = [
  { value: 'suggestion', label: '功能建议', icon: '💡' },
  { value: 'bug', label: '问题反馈', icon: '🐛' },
  { value: 'praise', label: '表扬鼓励', icon: '👍' },
  { value: 'other', label: '其他', icon: '💬' }
]
```

### 4. 数据收集
反馈数据包含以下字段：
- `type`: 反馈类型（suggestion/bug/praise/other）
- `content`: 反馈内容（必填，最少10字符，最多500字符）
- `contact`: 联系方式（选填，最多50字符）
- `timestamp`: 提交时间戳
- `userId`: 用户ID（如果已登录）
- `deviceInfo`: 设备信息对象

### 5. 设备信息收集
```javascript
const deviceInfo = {
  platform: systemInfo.platform,      // 平台（ios/android）
  system: systemInfo.system,          // 系统版本
  version: systemInfo.version,        // 微信版本
  model: systemInfo.model,            // 设备型号
  brand: systemInfo.brand,            // 设备品牌
  screenWidth: systemInfo.screenWidth,   // 屏幕宽度
  screenHeight: systemInfo.screenHeight  // 屏幕高度
}
```

## 后端接口设计

### 1. 反馈提交接口

**接口地址**: `POST /feedback/submit`

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token} (可选，如果用户已登录)
```

**请求参数**:
```json
{
  "type": "suggestion",
  "content": "建议增加更多简历模板",
  "contact": "<EMAIL>",
  "timestamp": 1703123456789,
  "userId": "user_12345",
  "deviceInfo": {
    "platform": "ios",
    "system": "iOS 16.0",
    "version": "8.0.0",
    "model": "iPhone 14",
    "brand": "Apple",
    "screenWidth": 390,
    "screenHeight": 844
  }
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "反馈提交成功",
  "feedbackId": "feedback_1703123456789_abc123"
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "反馈内容不能为空",
  "code": "INVALID_CONTENT"
}
```

## 数据库设计

### 1. 反馈表 (feedbacks)

```sql
CREATE TABLE feedbacks (
  id VARCHAR(50) PRIMARY KEY,           -- 反馈ID
  user_id VARCHAR(50),                  -- 用户ID（可为空）
  type ENUM('suggestion', 'bug', 'praise', 'other') NOT NULL, -- 反馈类型
  content TEXT NOT NULL,                -- 反馈内容
  contact VARCHAR(100),                 -- 联系方式
  device_info JSON,                     -- 设备信息
  status ENUM('pending', 'processing', 'resolved', 'closed') DEFAULT 'pending', -- 处理状态
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium', -- 优先级
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间
  processed_by VARCHAR(50),             -- 处理人员ID
  response TEXT,                        -- 回复内容
  response_at TIMESTAMP,                -- 回复时间
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
```

### 2. 反馈统计表 (feedback_stats)

```sql
CREATE TABLE feedback_stats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  date DATE NOT NULL,                   -- 统计日期
  type VARCHAR(20) NOT NULL,            -- 反馈类型
  count INT DEFAULT 0,                  -- 数量
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_date_type (date, type),
  INDEX idx_date (date)
);
```

## 服务器端实现建议

### 1. 接口实现 (Node.js + Express)

```javascript
// routes/feedback.js
const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');

// 提交反馈
router.post('/submit', async (req, res) => {
  try {
    const {
      type,
      content,
      contact,
      timestamp,
      userId,
      deviceInfo
    } = req.body;

    // 验证必填字段
    if (!type || !content) {
      return res.json({
        success: false,
        message: '反馈类型和内容不能为空',
        code: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 验证内容长度
    if (content.length < 10) {
      return res.json({
        success: false,
        message: '反馈内容至少需要10个字符',
        code: 'CONTENT_TOO_SHORT'
      });
    }

    if (content.length > 500) {
      return res.json({
        success: false,
        message: '反馈内容不能超过500个字符',
        code: 'CONTENT_TOO_LONG'
      });
    }

    // 生成反馈ID
    const feedbackId = `feedback_${Date.now()}_${uuidv4().substr(0, 8)}`;

    // 保存到数据库
    const feedback = {
      id: feedbackId,
      user_id: userId || null,
      type: type,
      content: content,
      contact: contact || null,
      device_info: JSON.stringify(deviceInfo || {}),
      status: 'pending',
      priority: determinePriority(type, content), // 根据类型和内容确定优先级
      created_at: new Date()
    };

    await saveFeedback(feedback);

    // 更新统计
    await updateFeedbackStats(type);

    // 发送通知（可选）
    await notifyAdmins(feedback);

    res.json({
      success: true,
      message: '反馈提交成功',
      feedbackId: feedbackId
    });

  } catch (error) {
    console.error('提交反馈失败:', error);
    res.json({
      success: false,
      message: '服务器错误，请稍后重试',
      code: 'SERVER_ERROR'
    });
  }
});

// 确定优先级
function determinePriority(type, content) {
  if (type === 'bug') {
    // 如果是bug反馈，检查关键词确定优先级
    const urgentKeywords = ['崩溃', '闪退', '无法使用', '数据丢失'];
    const highKeywords = ['错误', '异常', '失败'];
    
    const lowerContent = content.toLowerCase();
    
    if (urgentKeywords.some(keyword => lowerContent.includes(keyword))) {
      return 'urgent';
    } else if (highKeywords.some(keyword => lowerContent.includes(keyword))) {
      return 'high';
    }
  }
  
  return 'medium';
}

module.exports = router;
```

### 2. 数据库操作

```javascript
// models/feedback.js
const mysql = require('mysql2/promise');

async function saveFeedback(feedback) {
  const connection = await mysql.createConnection(dbConfig);
  
  const sql = `
    INSERT INTO feedbacks (
      id, user_id, type, content, contact, 
      device_info, status, priority, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  
  const values = [
    feedback.id,
    feedback.user_id,
    feedback.type,
    feedback.content,
    feedback.contact,
    feedback.device_info,
    feedback.status,
    feedback.priority,
    feedback.created_at
  ];
  
  await connection.execute(sql, values);
  await connection.end();
}

async function updateFeedbackStats(type) {
  const connection = await mysql.createConnection(dbConfig);
  const today = new Date().toISOString().split('T')[0];
  
  const sql = `
    INSERT INTO feedback_stats (date, type, count) 
    VALUES (?, ?, 1)
    ON DUPLICATE KEY UPDATE count = count + 1
  `;
  
  await connection.execute(sql, [today, type]);
  await connection.end();
}
```

## 管理后台功能建议

### 1. 反馈列表
- 按类型、状态、优先级筛选
- 按时间排序
- 搜索功能

### 2. 反馈处理
- 标记处理状态
- 添加回复
- 设置优先级

### 3. 统计分析
- 反馈数量趋势
- 类型分布
- 处理效率

## 通知机制

### 1. 实时通知
- 高优先级反馈立即通知管理员
- 可通过邮件、短信、企业微信等方式

### 2. 定期报告
- 每日反馈汇总
- 每周反馈分析报告

## 安全考虑

### 1. 防刷机制
- 同一用户限制提交频率
- IP限制
- 内容重复检测

### 2. 内容过滤
- 敏感词过滤
- 垃圾内容检测
- 恶意内容拦截

## 性能优化

### 1. 数据库优化
- 适当的索引
- 定期清理旧数据
- 分表策略

### 2. 缓存策略
- 统计数据缓存
- 热点数据缓存

这个文档提供了完整的Feedback功能实现方案，包括前端交互、后端接口、数据库设计和管理功能。
