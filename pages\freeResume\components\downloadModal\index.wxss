/* 下载链接浮窗组件样式 */
.download-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease;
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  z-index: 10;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

/* 模板信息 */
.template-info {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx 30rpx;
  background: #f8f9fa;
  margin: 20rpx 30rpx;
  border-radius: 12rpx;
}

.template-thumb {
  width: 80rpx;
  height: 100rpx;
  border-radius: 8rpx;
  background: #f0f0f0;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.template-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.template-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 下载链接区域 */
.download-links {
  padding: 0 30rpx;
}

.link-section {
  margin: 20rpx 0rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 0rpx;
  overflow: hidden;
}

.link-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #4B8BF5;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  color: white;
}

.platform-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  border-radius: 6rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.baidu-icon {
  background: #2932e1;
}

.quark-icon {
  background: #00d4aa;
}

.platform-name {
  font-size: 28rpx;
  font-weight: bold;
}

.link-content {
  padding: 20rpx;
  background: white;
}

.link-row {
  margin-bottom: 16rpx;
}

.link-row:last-child {
  margin-bottom: 0;
}

.link-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.link-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.link-value {
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
  background: #f8f9fa;
  padding: 16rpx 12rpx;
  border-radius: 8rpx;
  min-height: 28rpx;
}

.button-row {
  display: flex;
  gap: 12rpx;
  justify-content: center;
}

.action-btn {
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  min-width: 100rpx;
  height: 50rpx;
  line-height: 1;
  flex: 1;
  max-width: 120rpx;
}

.copy-btn {
  background: #667eea;
  color: white;
}

.copy-btn:disabled {
  background: #ccc;
  color: #999;
}

.copy-btn:active:not(:disabled) {
  background: #5a6fd8;
  transform: scale(0.98);
}

.share-btn {
  background: #28a745;
  color: white;
}

.share-btn:active {
  background: #218838;
  transform: scale(0.98);
}

/* 使用说明 */
.usage-tips {
  margin: 20rpx 30rpx 30rpx 30rpx;
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  overflow: hidden;
}

.tips-header {
  background: #ffeaa7;
  padding: 16rpx 20rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #856404;
}

.tips-content {
  padding: 20rpx;
}

.tip-item {
  display: block;
  font-size: 24rpx;
  color: #856404;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
