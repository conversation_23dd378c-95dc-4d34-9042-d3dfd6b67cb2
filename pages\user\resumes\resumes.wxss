/* pages/user/resumes/resumes.wxss */
.container {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 顶部操作栏样式 */
.action-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.create-btn {
  display: flex;
  align-items: center;
  background-color: #4B8BF5;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin: 0;
  line-height: 1.5;
}

.icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

/* 简历列表样式 */
.resume-list {
  width: 100%;
}

.resume-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid transparent;
}

.resume-item.current {
  border-color: #4B8BF5;
  background-color: #f6f9ff;
}

.resume-info {
  margin-bottom: 20rpx;
}

.resume-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.resume-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.current-badge {
  background-color: #4B8BF5;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: 15rpx;
}

.resume-meta {
  display: flex;
  flex-direction: column;
}

.resume-date {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.resume-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 40rpx;
}

.action-icon {
  font-size: 36rpx;
  margin-bottom: 6rpx;
}

.action-btn text:last-child {
  font-size: 24rpx;
  color: #666;
}

.preview-btn .action-icon {
  color: #4B8BF5;
}

.edit-btn .action-icon {
  color: #52c41a;
}

.delete-btn .action-icon {
  color: #ff4d4f;
}

.more-btn .action-icon {
  color: #666;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.create-btn-large {
  background-color: #4B8BF5;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4B8BF5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
