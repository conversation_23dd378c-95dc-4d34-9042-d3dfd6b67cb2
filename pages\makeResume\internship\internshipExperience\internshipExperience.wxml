<view class="container">
  <!-- 实习经历列表 -->
  <view class="internshipList" wx:if="{{internshipFormData.length > 0}}">
    <view class="internshipItem" 
          wx:for="{{internshipFormData}}" 
          wx:key="index"
          bindlongpress="handleLongPress"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd"
          data-index="{{index}}"
          style="{{currentIndex === index ? 'background: #f5f5f5; transform: scale(1.02);' : ''}}">
      <view class="itemContent" bindtap="editInternship" data-index="{{index}}">
        <view class="mainInfo">
          <text class="company">{{item.company}}</text>
          <text class="subText">{{item.position}}</text>
        </view>
        <text class="dateText">{{item.startDate}} - {{item.endDate}}</text>
      </view>
      <view class="actionButtons">
        <view class="editBtn" catchtap="editInternship" data-index="{{index}}">
          <text class="editIcon">✎</text>
        </view>
        <view class="deleteBtn" catchtap="deleteInternship" data-index="{{index}}">
          <text class="deleteIcon">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="emptyState" wx:else>
    <text class="emptyText">暂无实习经历，点击下方按钮添加</text>
  </view>

  <!-- 添加按钮 -->
  <view class="addBtn" bindtap="addInternship">
    + 添加实习经历
  </view>
</view> 