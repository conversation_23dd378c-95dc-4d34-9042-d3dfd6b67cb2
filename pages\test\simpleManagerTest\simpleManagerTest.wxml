<!--简化管理器测试页面-->
<view class="test-page">
  <view class="header">
    <text class="title">简化简历管理器测试</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="rerunTests">重新运行测试</button>
    <button class="action-btn secondary" bindtap="viewCurrentResume">查看当前简历</button>
    <button class="action-btn secondary" bindtap="createTestResume">创建测试简历</button>
    <button class="action-btn info" bindtap="testJobIntentionPage">测试求职意向</button>
  </view>

  <!-- 统计信息 -->
  <view wx:if="{{managerStats}}" class="stats-panel">
    <view class="panel-header">
      <text class="panel-title">管理器统计</text>
    </view>
    <view class="stats-content">
      <view class="stat-item">
        <text class="stat-label">总简历数:</text>
        <text class="stat-value">{{managerStats.totalResumes}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">当前简历:</text>
        <text class="stat-value">{{managerStats.currentResumeIndex || '无'}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">初始化状态:</text>
        <text class="stat-value {{managerStats.isInitialized ? 'success' : 'error'}}">
          {{managerStats.isInitialized ? '已初始化' : '未初始化'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 简历列表 -->
  <view wx:if="{{allResumes.length > 0}}" class="resumes-panel">
    <view class="panel-header">
      <text class="panel-title">简历列表 ({{allResumes.length}})</text>
    </view>
    <view class="resumes-list">
      <view wx:for="{{allResumes}}" wx:key="index" class="resume-item {{item.isCurrent ? 'current' : ''}}">
        <view class="resume-info">
          <text class="resume-title">{{item.title}}</text>
          <text class="resume-time">{{item.updateTime ? new Date(item.updateTime).toLocaleString() : '未知时间'}}</text>
        </view>
        <view class="resume-status">
          <text wx:if="{{item.isCurrent}}" class="current-badge">当前</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 当前简历信息 -->
  <view wx:if="{{currentResumeData}}" class="current-resume-panel">
    <view class="panel-header">
      <text class="panel-title">当前简历信息</text>
    </view>
    <view class="resume-content">
      <view class="info-item">
        <text class="info-label">标题:</text>
        <text class="info-value">{{currentResumeData.title}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">姓名:</text>
        <text class="info-value">{{currentResumeData.basicInfo.name || '未填写'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">手机:</text>
        <text class="info-value">{{currentResumeData.basicInfo.phone || '未填写'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">邮箱:</text>
        <text class="info-value">{{currentResumeData.basicInfo.email || '未填写'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">期望职位:</text>
        <text class="info-value">{{currentResumeData.jobIntention.position || '未填写'}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">期望薪资:</text>
        <text class="info-value">{{currentResumeData.jobIntention.salary || '未填写'}}</text>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-panel">
    <view class="panel-header">
      <text class="panel-title">测试结果 ({{testResults.length}})</text>
      <button class="clear-btn" bindtap="clearResults">清空</button>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view wx:for="{{testResults}}" wx:key="index" class="result-item">
        <view class="result-header">
          <text class="result-time">{{item.time}}</text>
        </view>
        <view class="result-message">{{item.message}}</view>
      </view>
    </scroll-view>
  </view>
</view>
