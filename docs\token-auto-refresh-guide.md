# Token自动刷新和无感重新授权解决方案

## 方案概述

本解决方案实现了一个完整的token自动刷新和无感重新授权系统，确保用户在token过期时能够无感知地继续使用应用，同时保证安全性。

## 核心特性

### 1. 自动Token刷新
- **预防性刷新**: 在token过期前5分钟自动刷新
- **失败重试**: 刷新失败时自动尝试重新登录
- **并发控制**: 避免多个请求同时触发刷新

### 2. 无感重新授权
- **透明处理**: 用户无需感知token过期和刷新过程
- **请求重试**: 认证失败的请求会在token刷新后自动重试
- **状态同步**: 全局状态和本地存储自动同步

### 3. 安全机制
- **Token有效期管理**: 严格控制token的生命周期
- **刷新Token保护**: 安全存储和使用refresh_token
- **错误处理**: 优雅处理各种异常情况

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   应用层API     │    │   Token管理器   │    │   网络请求层    │
│                 │    │                 │    │                 │
│ userApi.login() │───▶│ saveTokenInfo() │    │ request()       │
│ userApi.get()   │    │ getValidToken() │◀───│ executeRequest()│
│ resumeApi.xxx() │    │ refreshToken()  │    │ isAuthError()   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   本地存储      │              │
         │              │                 │              │
         │              │ tokenInfo       │              │
         │              │ userInfo        │              │
         │              │ membershipInfo  │              │
         │              └─────────────────┘              │
         │                                               │
         └───────────────────────────────────────────────┘
```

## 使用方法

### 1. 基本API调用
```javascript
// 所有API调用都会自动处理token刷新
const userApi = require('../../utils/api/userApi');

// 这个调用会自动检查token有效性，必要时刷新
const userInfo = await userApi.getUserInfo();
```

### 2. 检查Token状态
```javascript
const tokenManager = require('../../utils/auth/tokenManager');

// 检查token状态
const status = tokenManager.getTokenStatus();
console.log('Token状态:', status); // 'valid', 'expired', 'refreshing', 'invalid'

// 检查是否即将过期
const isExpiring = tokenManager.isTokenExpiringSoon();
console.log('是否即将过期:', isExpiring);
```

### 3. 手动刷新Token
```javascript
const tokenManager = require('../../utils/auth/tokenManager');

try {
  const newToken = await tokenManager.getValidToken(true); // 强制刷新
  console.log('Token刷新成功:', newToken);
} catch (error) {
  console.error('Token刷新失败:', error);
}
```

## 工作流程

### 1. 正常请求流程
```
用户发起API请求
    ↓
检查token有效性
    ↓
token有效 → 直接发起请求 → 返回结果
```

### 2. Token即将过期流程
```
用户发起API请求
    ↓
检查token有效性
    ↓
token即将过期 → 自动刷新token → 使用新token发起请求 → 返回结果
```

### 3. Token已过期流程
```
用户发起API请求
    ↓
发起请求
    ↓
服务器返回401
    ↓
尝试刷新token
    ↓
刷新成功 → 重试原请求 → 返回结果
刷新失败 → 自动重新登录 → 重试原请求 → 返回结果
```

### 4. 完全失效流程
```
用户发起API请求
    ↓
尝试刷新token失败
    ↓
尝试自动重新登录失败
    ↓
提示用户重新登录
```

## 配置说明

### 1. Token有效期配置
```javascript
// tokenManager.js
const BUFFER_TIME = 300; // 5分钟缓冲时间
const DEFAULT_EXPIRES_IN = 1800; // 默认30分钟有效期
```

### 2. 服务端接口配置
```javascript
// 需要服务端支持的接口
POST /auth/login     // 登录接口
POST /auth/refresh   // 刷新token接口
GET  /auth/user      // 验证token接口
```

### 3. 请求头格式
```javascript
{
  "Authorization": "Bearer <access_token>",
  "X-User-Id": "<user_id>",
  "Content-Type": "application/json"
}
```

## 错误处理

### 1. 网络错误
- 自动重试机制
- 用户友好的错误提示
- 降级到缓存数据

### 2. 认证错误
- 自动token刷新
- 无感重新登录
- 最终失败时提示用户

### 3. 服务器错误
- 区分临时错误和永久错误
- 适当的重试策略
- 详细的错误日志

## 最佳实践

### 1. API调用
```javascript
// ✅ 推荐：使用封装好的API方法
const userApi = require('../../utils/api/userApi');
const data = await userApi.getUserInfo();

// ❌ 不推荐：直接使用wx.request
wx.request({
  url: '/auth/user',
  header: { 'Authorization': 'Bearer xxx' }
});
```

### 2. 错误处理
```javascript
// ✅ 推荐：让系统自动处理认证错误
try {
  const data = await userApi.getUserInfo();
  // 处理成功数据
} catch (error) {
  // 只处理业务逻辑错误
  if (!error.isAuthError) {
    // 处理非认证错误
  }
}
```

### 3. 状态检查
```javascript
// ✅ 推荐：使用tokenManager检查状态
const tokenManager = require('../../utils/auth/tokenManager');
if (tokenManager.needsLogin()) {
  // 引导用户登录
}

// ❌ 不推荐：直接检查本地存储
const token = wx.getStorageSync('userToken');
if (!token) {
  // 可能不准确
}
```

## 测试和调试

### 1. 使用测试工具
```javascript
const tokenTest = require('../../utils/auth/tokenRefreshTest');

// 运行完整测试套件
await tokenTest.runFullTestSuite();

// 模拟token过期
tokenTest.simulateTokenExpiry();

// 开始监控
const stopMonitoring = tokenTest.startTokenMonitoring();
```

### 2. 调试日志
```javascript
// 开启详细日志
console.log('Token状态:', tokenManager.getTokenStatus());
console.log('Token信息:', tokenManager.getTokenInfo());
```

## 服务端要求

### 1. 登录接口响应格式
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 1800,
  "token_type": "bearer",
  "user_info": {
    "id": 1,
    "nickname": "用户昵称",
    "is_member": true
  }
}
```

### 2. 刷新接口请求格式
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 3. 错误响应格式
```json
{
  "detail": "Token已过期",
  "error_code": "TOKEN_EXPIRED"
}
```

## 安全考虑

### 1. Token存储
- access_token存储在内存和本地存储中
- refresh_token安全存储，定期轮换
- 敏感信息不在日志中输出

### 2. 网络安全
- 所有请求使用HTTPS
- 请求头包含必要的安全信息
- 防止token泄露

### 3. 异常处理
- 优雅处理各种异常情况
- 避免无限递归刷新
- 及时清理过期信息

## 性能优化

### 1. 减少网络请求
- 预防性token刷新
- 智能的过期时间检查
- 避免重复刷新

### 2. 用户体验
- 无感知的token刷新
- 最小化loading时间
- 友好的错误提示

### 3. 资源管理
- 及时清理过期数据
- 合理的缓存策略
- 内存使用优化

这个解决方案提供了一个完整、安全、用户友好的token自动刷新机制，确保用户在使用应用时不会因为token过期而中断操作。
