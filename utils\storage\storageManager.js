/**
 * 存储管理工具
 * 用于管理微信小程序的本地文件存储，防止存储空间超限
 */

class StorageManager {
  constructor() {
    this.userDataPath = wx.env.USER_DATA_PATH;
    this.fs = wx.getFileSystemManager();
    
    // 存储限制配置
    this.config = {
      // 总存储限制 (50MB)
      maxTotalSize: 50 * 1024 * 1024,
      
      // 各类文件的限制
      limits: {
        preview: {
          maxFiles: 2,
          maxSize: 5 * 1024 * 1024, // 5MB
          pattern: /^preview_.*\.jpg$/
        },
        pdf: {
          maxFiles: 3,
          maxSize: 20 * 1024 * 1024, // 20MB
          pattern: /\.pdf$/
        },
        jpg: {
          maxFiles: 10,
          maxSize: 5 * 1024 * 1024, // 20MB
          pattern: /\.pdf$/
        },
        temp: {
          maxFiles: 5,
          maxSize: 10 * 1024 * 1024, // 10MB
          pattern: /^temp_.*\.(jpg|png|pdf)$/
        }
      },
      
      // 警告阈值 (80%)
      warningThreshold: 0.8
    };
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取目录中所有文件的信息
   */
  async getDirectoryInfo() {
    return new Promise((resolve) => {
      this.fs.readdir({
        dirPath: this.userDataPath,
        success: (res) => {
          const filePromises = res.files.map(file => {
            return new Promise((fileResolve) => {
              const filePath = `${this.userDataPath}/${file}`;
              this.fs.stat({
                path: filePath,
                success: (stat) => {
                  fileResolve({
                    name: file,
                    path: filePath,
                    size: stat.stats.size,
                    mtime: stat.stats.mtime,
                    type: this.getFileType(file)
                  });
                },
                fail: () => fileResolve(null)
              });
            });
          });

          Promise.all(filePromises).then(fileInfos => {
            const validFiles = fileInfos.filter(info => info !== null);
            const totalSize = validFiles.reduce((sum, file) => sum + file.size, 0);
            
            resolve({
              files: validFiles,
              totalSize,
              totalCount: validFiles.length
            });
          });
        },
        fail: (error) => {
          console.warn('读取目录失败:', error);
          resolve({ files: [], totalSize: 0, totalCount: 0 });
        }
      });
    });
  }

  /**
   * 根据文件名判断文件类型
   */
  getFileType(fileName) {
    for (const [type, config] of Object.entries(this.config.limits)) {
      if (config.pattern.test(fileName)) {
        return type;
      }
    }
    return 'other';
  }

  /**
   * 检查存储状态
   */
  async checkStorageStatus() {
    const dirInfo = await this.getDirectoryInfo();
    const usageRatio = dirInfo.totalSize / this.config.maxTotalSize;
    
    const status = {
      totalSize: dirInfo.totalSize,
      totalSizeFormatted: this.formatFileSize(dirInfo.totalSize),
      maxSize: this.config.maxTotalSize,
      maxSizeFormatted: this.formatFileSize(this.config.maxTotalSize),
      usageRatio,
      usagePercentage: Math.round(usageRatio * 100),
      isWarning: usageRatio >= this.config.warningThreshold,
      isFull: usageRatio >= 0.95,
      fileCount: dirInfo.totalCount,
      files: dirInfo.files
    };

    // 按类型统计
    status.byType = {};
    for (const type of Object.keys(this.config.limits)) {
      const typeFiles = dirInfo.files.filter(f => f.type === type);
      status.byType[type] = {
        count: typeFiles.length,
        size: typeFiles.reduce((sum, f) => sum + f.size, 0),
        files: typeFiles
      };
    }
    return status;
  }

  /**
   * 清理指定类型的文件
   */
  async cleanupFilesByType(type) {
    const config = this.config.limits[type];
    if (!config) {
      console.warn(`未知的文件类型: ${type}`);
      return { deleted: 0, savedSpace: 0 };
    }

    const dirInfo = await this.getDirectoryInfo();
    console.log(`dir files : ${dirInfo.files}`);
    const typeFiles = dirInfo.files.filter(f => f.type === type);
    const fileNames = dirInfo.files.map(f => f.name);
    console.log(`fileNames : ${fileNames}`);
    if (typeFiles.length === 0) {
      return { deleted: 0, savedSpace: 0 };
    }

    // 按修改时间排序（最新的在前）
    typeFiles.sort((a, b) => b.mtime - a.mtime);

    let filesToDelete = [];
    let currentSize = 0;
    let keepCount = 0;

    for (const file of typeFiles) {
      if (keepCount < config.maxFiles && currentSize + file.size <= config.maxSize) {
        currentSize += file.size;
        keepCount++;
      } else {
        filesToDelete.push(file);
      }
    }

    // 执行删除
    let deletedCount = 0;
    let savedSpace = 0;

    for (const file of filesToDelete) {
      try {
        await this.deleteFile(file.path);
        deletedCount++;
        savedSpace += file.size;
        console.log(`删除${type}文件: ${file.name} (${this.formatFileSize(file.size)})`);
      } catch (error) {
        console.warn(`删除${type}文件失败: ${file.name}`, error);
      }
    }

    return { deleted: deletedCount, savedSpace };
  }

  /**
   * 删除单个文件
   */
  deleteFile(filePath) {
    return new Promise((resolve, reject) => {
      this.fs.unlink({
        filePath,
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 全面清理存储空间
   */
  async performFullCleanup() {
    console.log('开始全面清理存储空间...');
    
    const beforeStatus = await this.checkStorageStatus();
    console.log(`清理前: ${beforeStatus.totalSizeFormatted} (${beforeStatus.usagePercentage}%)`);

    const results = {};
    let totalDeleted = 0;
    let totalSavedSpace = 0;

    // 按优先级清理各类文件
    const cleanupOrder = ['temp', 'preview', 'pdf'];
    
    for (const type of cleanupOrder) {
      const result = await this.cleanupFilesByType(type);
      results[type] = result;
      totalDeleted += result.deleted;
      totalSavedSpace += result.savedSpace;
      
      console.log(`${type}文件清理完成: 删除${result.deleted}个文件，节省${this.formatFileSize(result.savedSpace)}`);
    }

    const afterStatus = await this.checkStorageStatus();
    console.log(`清理后: ${afterStatus.totalSizeFormatted} (${afterStatus.usagePercentage}%)`);

    return {
      before: beforeStatus,
      after: afterStatus,
      results,
      totalDeleted,
      totalSavedSpace: this.formatFileSize(totalSavedSpace)
    };
  }

  /**
   * 应用启动时的存储检查和清理
   */
  async initCleanup() {
    try {
      const status = await this.checkStorageStatus();
      
      console.log(`存储状态检查: ${status.totalSizeFormatted}/${status.maxSizeFormatted} (${status.usagePercentage}%)`);
      
      // 如果超过警告阈值，执行清理
      if (status.isWarning) {
        console.log('存储空间不足，开始自动清理...');
        const cleanupResult = await this.performFullCleanup();
        
        // 显示清理结果
        if (cleanupResult.totalDeleted > 0) {
          wx.showToast({
            title: `已清理${cleanupResult.totalDeleted}个文件`,
            icon: 'success',
            duration: 2000
          });
        }
      }
      
      return status;
    } catch (error) {
      console.error('存储初始化清理失败:', error);
      return null;
    }
  }
}

// 创建单例实例
const storageManager = new StorageManager();

module.exports = storageManager;
