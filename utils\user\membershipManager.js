/**
 * 会员状态管理工具
 * 提供会员状态查询、缓存和同步功能
 */

const userApi = require('../api/userApi');

/**
 * 查询并更新会员状态
 * @param {boolean} forceRefresh 是否强制刷新，默认false
 * @returns {Promise<Object>} 会员状态信息
 */
function queryMemberStatus(forceRefresh = false) {
  return new Promise((resolve, reject) => {
    // 如果不强制刷新，先检查缓存
    if (!forceRefresh) {
      const cachedInfo = getCachedMemberInfo();
      if (cachedInfo && !isCacheExpired(cachedInfo)) {
        console.log('使用缓存的会员信息:', cachedInfo);
        resolve(cachedInfo);
        return;
      }
    }

    // 从服务器查询会员状态
    console.log('从服务器查询会员状态...');
    userApi.getMemberStatus()
      .then((res) => {
        console.log('会员状态查询成功:', res);
        
        // 构建会员信息对象
        const membershipInfo = {
          isMember: res.is_member || false,
          openid: res.openid || '',
          message: res.message || '',
          lastUpdated: Date.now()
        };

        // 保存到本地存储
        saveMemberInfo(membershipInfo);

        // 更新全局状态
        updateGlobalMemberStatus(membershipInfo);

        resolve(membershipInfo);
      })
      .catch((err) => {
        console.error('查询会员状态失败:', err);
        
        // 查询失败时，返回缓存信息或默认信息
        const cachedInfo = getCachedMemberInfo();
        if (cachedInfo) {
          console.log('查询失败，使用缓存信息:', cachedInfo);
          resolve(cachedInfo);
        } else {
          // 返回默认的非会员状态
          const defaultInfo = {
            isMember: false,
            openid: '',
            message: '普通用户',
            lastUpdated: Date.now()
          };
          resolve(defaultInfo);
        }
      });
  });
}

/**
 * 获取缓存的会员信息
 * @returns {Object|null} 缓存的会员信息
 */
function getCachedMemberInfo() {
  try {
    const membershipInfo = wx.getStorageSync('membershipInfo');
    return membershipInfo || null;
  } catch (error) {
    console.error('获取缓存会员信息失败:', error);
    return null;
  }
}

/**
 * 检查缓存是否过期
 * @param {Object} membershipInfo 会员信息
 * @returns {boolean} 是否过期
 */
function isCacheExpired(membershipInfo) {
  if (!membershipInfo || !membershipInfo.lastUpdated) {
    return true;
  }
  
  // 缓存有效期：30分钟
  const CACHE_DURATION = 30 * 60 * 1000;
  const now = Date.now();
  return (now - membershipInfo.lastUpdated) > CACHE_DURATION;
}

/**
 * 保存会员信息到本地存储
 * @param {Object} membershipInfo 会员信息
 */
function saveMemberInfo(membershipInfo) {
  try {
    wx.setStorageSync('membershipInfo', membershipInfo);
    console.log('会员信息已保存到本地存储:', membershipInfo);
  } catch (error) {
    console.error('保存会员信息失败:', error);
  }
}

/**
 * 更新全局会员状态
 * @param {Object} membershipInfo 会员信息
 */
function updateGlobalMemberStatus(membershipInfo) {
  const app = getApp();
  if (app && app.globalData) {
    app.globalData.isMember = membershipInfo.isMember;
    app.globalData.membershipInfo = membershipInfo;
    console.log('全局会员状态已更新:', membershipInfo);
  }
}

/**
 * 获取当前会员状态（同步方法）
 * @returns {boolean} 是否为会员
 */
function isMember() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.hasOwnProperty('isMember')) {
    return app.globalData.isMember;
  }
  
  // 从本地存储获取
  const membershipInfo = getCachedMemberInfo();
  if (membershipInfo && !isCacheExpired(membershipInfo)) {
    return membershipInfo.isMember;
  }
  
  // 默认返回非会员
  return false;
}

/**
 * 获取会员状态信息（同步方法）
 * @returns {Object} 会员状态信息
 */
function getMemberInfo() {
  // 优先从全局数据获取
  const app = getApp();
  if (app && app.globalData && app.globalData.membershipInfo) {
    return app.globalData.membershipInfo;
  }
  
  // 从本地存储获取
  const membershipInfo = getCachedMemberInfo();
  if (membershipInfo) {
    return membershipInfo;
  }
  
  // 返回默认信息
  return {
    isMember: false,
    openid: '',
    message: '普通用户',
    lastUpdated: 0
  };
}

/**
 * 清除会员状态信息
 */
function clearMemberInfo() {
  try {
    wx.removeStorageSync('membershipInfo');
    
    // 清除全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.isMember = false;
      app.globalData.membershipInfo = null;
    }
    
    console.log('会员状态信息已清除');
  } catch (error) {
    console.error('清除会员信息失败:', error);
  }
}

/**
 * 在登录后自动查询会员状态
 * @param {boolean} silent 是否静默查询，默认true
 */
function queryMemberStatusAfterLogin(silent = true) {
  console.log('登录后查询会员状态...');
  
  // 延迟查询，确保登录流程完成
  setTimeout(() => {
    queryMemberStatus(true)
      .then((membershipInfo) => {
        console.log('登录后会员状态查询完成:', membershipInfo);
        
        if (!silent) {
          // 如果不是静默查询，可以显示会员状态提示
          const statusText = membershipInfo.isMember ? '会员用户' : '普通用户';
          wx.showToast({
            title: `当前状态: ${statusText}`,
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch((err) => {
        console.error('登录后会员状态查询失败:', err);
      });
  }, 800);
}

module.exports = {
  queryMemberStatus,
  getCachedMemberInfo,
  isCacheExpired,
  saveMemberInfo,
  updateGlobalMemberStatus,
  isMember,
  getMemberInfo,
  clearMemberInfo,
  queryMemberStatusAfterLogin
};
