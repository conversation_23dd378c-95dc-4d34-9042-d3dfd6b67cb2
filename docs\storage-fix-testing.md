# 存储空间超限问题修复测试指南

## 问题描述

在微信小程序测试环境中，多次生成简历预览图或PDF后，会出现以下错误：
```
writeFileSync:fail the maximum size of the file storage limit is exceeded
```

## 修复内容

### 1. 增强预览图片文件清理机制
- **文件**: `pages/makeCreateResume/components/resumePreview/index.js`
- **改进**: 
  - 基于文件大小和数量的智能清理策略
  - 保留最新2个文件，总大小不超过5MB
  - 按修改时间排序，优先删除旧文件

### 2. 新增PDF文件清理机制
- **文件**: `pages/makeCreateResume/makeCreateResume.js`
- **改进**:
  - 在生成PDF前自动清理旧文件
  - 保留最新3个文件，总大小不超过20MB
  - 防止PDF文件无限累积

### 3. 优化缓存清理逻辑
- **文件**: `pages/makeCreateResume/components/resumePreview/index.js`
- **改进**:
  - 清理内存缓存时同步删除对应的物理文件
  - 防止缓存过期但文件仍占用空间的问题

### 4. 创建存储管理工具
- **文件**: `utils/storage/storageManager.js`
- **功能**:
  - 统一的文件类型管理
  - 存储状态监控
  - 全面清理功能
  - 应用启动时自动检查和清理

### 5. 应用启动时存储检查
- **文件**: `app.js`
- **功能**:
  - 应用启动时检查存储状态
  - 超过80%使用率时自动清理
  - 用户友好的清理提示

### 6. 设置页面存储管理
- **文件**: `pages/user/settings/settings.*`
- **功能**:
  - 实时显示存储使用情况
  - 手动清理存储空间
  - 可视化存储状态

## 测试步骤

### 测试1: 预览图片清理测试

1. **准备工作**:
   - 打开微信开发者工具
   - 进入简历制作页面

2. **执行测试**:
   - 连续生成10-15次预览图片（切换模板、修改配置等）
   - 观察控制台日志，确认清理机制正常工作
   - 检查存储目录，确认只保留最新的2个预览文件

3. **预期结果**:
   ```
   发现 X 个预览图片文件
   预览图片总大小: X.XX MB
   准备删除 X 个旧预览文件
   删除旧预览文件成功: preview_xxx.jpg (X.XX MB)
   ```

### 测试2: PDF文件清理测试

1. **执行测试**:
   - 连续生成5-8个PDF文件
   - 观察控制台日志，确认清理机制正常工作
   - 检查存储目录，确认只保留最新的3个PDF文件

2. **预期结果**:
   ```
   发现 X 个PDF文件
   PDF文件总大小: X.XX MB
   准备删除 X 个旧PDF文件
   删除旧PDF文件成功: resume_xxx.pdf (X.XX MB)
   ```

### 测试3: 存储状态监控测试

1. **执行测试**:
   - 进入设置页面
   - 查看存储管理区域
   - 确认显示正确的存储使用情况

2. **预期结果**:
   - 显示当前存储使用量和百分比
   - 进度条颜色正确（正常蓝色，警告红色）
   - 文件数量统计正确

### 测试4: 手动清理测试

1. **执行测试**:
   - 在设置页面点击"清理存储空间"
   - 确认清理对话框显示
   - 执行清理并观察结果

2. **预期结果**:
   ```
   清理完成！删除了X个文件，节省X.XX MB空间。
   ```

### 测试5: 应用启动清理测试

1. **准备工作**:
   - 生成大量文件使存储使用率超过80%
   - 重新启动小程序

2. **预期结果**:
   - 控制台显示存储检查日志
   - 如果超过阈值，自动执行清理
   - 显示清理结果提示

### 测试6: 存储超限压力测试

1. **执行测试**:
   - 在修复前的环境中重现原问题
   - 应用修复后的代码
   - 重复执行大量文件生成操作
   - 确认不再出现存储超限错误

2. **预期结果**:
   - 不再出现 "maximum size of the file storage limit is exceeded" 错误
   - 存储使用量保持在合理范围内
   - 应用运行稳定

## 监控指标

### 存储使用情况
- **总存储限制**: 50MB
- **预览图片限制**: 最多2个文件，总计5MB
- **PDF文件限制**: 最多3个文件，总计20MB
- **临时文件限制**: 最多5个文件，总计10MB

### 清理触发条件
- **自动清理**: 存储使用率 ≥ 80%
- **文件数量超限**: 超过各类型文件数量限制
- **文件大小超限**: 超过各类型文件大小限制

### 性能指标
- **清理响应时间**: < 2秒
- **存储检查时间**: < 1秒
- **文件删除成功率**: > 95%

## 故障排除

### 常见问题

1. **清理功能不工作**
   - 检查存储管理器是否正确引入
   - 确认文件路径和权限正确
   - 查看控制台错误日志

2. **存储状态显示异常**
   - 检查存储状态加载函数
   - 确认数据绑定正确
   - 重新加载页面

3. **文件删除失败**
   - 检查文件是否被占用
   - 确认文件路径正确
   - 查看微信开发者工具文件系统

### 调试方法

1. **开启详细日志**:
   ```javascript
   // 在存储管理器中添加更多日志
   console.log('详细的文件信息:', fileInfo);
   ```

2. **手动检查文件系统**:
   - 在微信开发者工具中查看 Storage 面板
   - 检查 USER_DATA_PATH 目录内容

3. **监控存储使用**:
   ```javascript
   // 定期检查存储状态
   setInterval(async () => {
     const status = await storageManager.checkStorageStatus();
     console.log('当前存储状态:', status);
   }, 30000);
   ```

## 验收标准

### 功能验收
- ✅ 不再出现存储超限错误
- ✅ 预览图片文件自动清理
- ✅ PDF文件自动清理
- ✅ 缓存过期文件同步删除
- ✅ 应用启动时自动检查
- ✅ 设置页面存储管理功能

### 性能验收
- ✅ 存储使用率保持在合理范围（< 80%）
- ✅ 文件清理响应及时（< 2秒）
- ✅ 不影响正常功能使用
- ✅ 用户体验良好

### 稳定性验收
- ✅ 连续使用不出现存储问题
- ✅ 清理功能稳定可靠
- ✅ 异常情况处理得当
- ✅ 日志信息完整清晰
