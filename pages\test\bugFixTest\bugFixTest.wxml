<!--Bug修复测试页面-->
<view class="test-page">
  <view class="header">
    <text class="title">Bug修复测试</text>
    <text class="subtitle">测试求职意向字段更新修复</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="rerunTests">重新运行测试</button>
    <button class="action-btn secondary" bindtap="testJobIntentionPage">测试求职意向页面</button>
    <button class="action-btn info" bindtap="manualUpdateJobIntention">手动更新测试</button>
  </view>

  <!-- 当前求职意向数据 -->
  <view wx:if="{{currentJobIntention}}" class="data-panel">
    <view class="panel-header">
      <text class="panel-title">当前求职意向数据</text>
    </view>
    <view class="data-content">
      <view class="data-item">
        <text class="data-label">期望职位:</text>
        <text class="data-value">{{currentJobIntention.position || '未填写'}}</text>
      </view>
      <view class="data-item">
        <text class="data-label">期望城市:</text>
        <text class="data-value">{{currentJobIntention.city || '未填写'}}</text>
      </view>
      <view class="data-item">
        <text class="data-label">期望薪资:</text>
        <text class="data-value">{{currentJobIntention.salary || '未填写'}}</text>
      </view>
      <view class="data-item">
        <text class="data-label">求职状态:</text>
        <text class="data-value">{{currentJobIntention.status || '未填写'}}</text>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-panel">
    <view class="panel-header">
      <text class="panel-title">测试结果 ({{testResults.length}})</text>
      <button class="clear-btn" bindtap="clearResults">清空</button>
    </view>
    
    <scroll-view class="results-list" scroll-y="true">
      <view wx:for="{{testResults}}" wx:key="index" class="result-item">
        <view class="result-header">
          <text class="result-time">{{item.time}}</text>
        </view>
        <view class="result-message">{{item.message}}</view>
      </view>
    </scroll-view>
  </view>

  <!-- 说明信息 -->
  <view class="info-panel">
    <view class="panel-header">
      <text class="panel-title">修复说明</text>
    </view>
    <view class="info-content">
      <text class="info-item">🐛 问题: updateField时普通对象没有转换为类实例</text>
      <text class="info-item">🔧 修复: 添加convertFieldValue方法进行类型转换</text>
      <text class="info-item">✅ 结果: jobIntention.toObject()方法正常调用</text>
      <text class="info-item">🧪 测试: 验证字段更新、类型转换、数据持久化</text>
    </view>
  </view>
</view>
