const ResumeFormHelper = require('../../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    workFormData: [],  // 工作经历表单数据
    currentIndex: -1,      // 当前拖动项的索引
    startY: 0,            // 开始触摸的纵坐标
    moveY: 0             // 移动的纵坐标
  },

  onLoad() {
    console.log('=== 工作经历页面加载 ===');
    this.loadWorkData();
  },

  /**
   * 从全局管理器加载工作经历数据
   */
  loadWorkData() {
    try {
      const workData = ResumeFormHelper.loadFieldData('work', app);

      this.setData({
        workFormData: workData
      });

      console.log('✅ 工作经历数据加载成功:', workData);
    } catch (error) {
      console.error('❌ 加载工作经历数据失败:', error);
      // 出错时使用空数据
      this.setData({
        workFormData: []
      });
    }
  },

  // 长按触发
  handleLongPress(e) {
    if (!Array.isArray(this.data.workFormData)) {
      this.setData({ workFormData: [] });
      return;
    }
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY
    });
  },

  // 触摸移动
  touchMove(e) {
    if (this.data.currentIndex < 0 || !Array.isArray(this.data.workFormData)) return;

    const moveY = e.touches[0].clientY;
    const moveDistance = moveY - this.data.startY;

    // 计算目标位置
    const itemHeight = 120; // 每个项目的高度，根据实际调整
    const moveIndex = Math.round(moveDistance / itemHeight);

    let targetIndex = this.data.currentIndex + moveIndex;
    targetIndex = Math.max(0, Math.min(targetIndex, this.data.workFormData.length - 1));

    if (targetIndex !== this.data.currentIndex) {
      // 交换位置
      const workFormData = [...this.data.workFormData];
      const temp = workFormData[this.data.currentIndex];
      workFormData[this.data.currentIndex] = workFormData[targetIndex];
      workFormData[targetIndex] = temp;

      this.setData({
        workFormData,
        currentIndex: targetIndex,
        startY: moveY
      });
    }
  },

  // 触摸结束
  touchEnd() {
    if (this.data.currentIndex >= 0 && Array.isArray(this.data.workFormData)) {
      try {
        // 更新所有项的sortIndex
        const workFormData = this.data.workFormData.map((item, index) => ({
          ...item,
          sortIndex: index
        }));

        // 保存排序后的数据
        this.setData({
          workFormData,
          currentIndex: -1,
          startY: 0,
          moveY: 0
        });

        // 更新存储
        wx.setStorageSync('workList', workFormData);
      } catch (error) {
        console.error('保存排序数据失败:', error);
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        // 确保即使保存失败，数据状态也是正确的
        this.setData({
          currentIndex: -1,
          startY: 0,
          moveY: 0
        });
      }
    }
  },

  // 添加工作经历
  addWork() {
    wx.navigateTo({
      url: '../workEdit/workEdit',
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 编辑工作经历
  editWork(e) {
    const index = e.currentTarget.dataset.index;
    wx.navigateTo({
      url: `../workEdit/workEdit?index=${index}`,
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除工作经历
  deleteWork(e) {
    if (!Array.isArray(this.data.workFormData)) {
      this.setData({ workFormData: [] });
      return;
    }

    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '提示',
      content: '确定要删除这条工作经历吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            const workFormData = [...this.data.workFormData];
            workFormData.splice(index, 1);

            // 使用 ResumeFormHelper 统一保存
            const success = ResumeFormHelper.saveFieldData('work', workFormData, app);

            if (success) {
              this.setData({ workFormData });

              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });

              console.log('✅ 工作经历删除成功');
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
              console.error('❌ 工作经历删除失败');
            }
          } catch (error) {
            console.error('❌ 删除工作经历时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    this.loadWorkData();
  }
});