const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    awardsList: null  // 将在 onLoad 中初始化
  },

  onLoad() {
    console.log('=== 奖项证书页面加载 ===');
    this.loadAwardsData();
  },

  /**
   * 从全局管理器加载奖项证书数据
   */
  loadAwardsData() {
    try {
      const awardsData = ResumeFormHelper.loadFieldData('awards', app);

      // awardsData 现在是一个包含16个元素的数组（包括空字符串）
      this.setData({
        awardsList: awardsData
      });

      console.log('✅ 奖项证书数据加载成功:', awardsData);
    } catch (error) {
      console.error('❌ 加载奖项证书数据失败:', error);
      // 出错时使用空数据
      const emptyData = ResumeFormHelper.getEmptyFieldData('awards');
      this.setData({
        awardsList: emptyData
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;

    const awardsList = [...this.data.awardsList];
    awardsList[index] = value;

    this.setData({
      awardsList: awardsList
    });
  },

  /**
   * 保存奖项证书信息
   */
  saveInfo() {
    try {
      // 过滤掉空字符串，并确保每个项都是字符串
      const awardsList = this.data.awardsList
        .map(item => {
          if (typeof item === 'object' && item !== null) {
            return item.content || '';
          }
          return item;
        })
        .filter(item => typeof item === 'string' && item.trim() !== '');

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('awards', awardsList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 奖项证书保存成功:', awardsList);

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 奖项证书保存失败');
      }
    } catch (error) {
      console.error('❌ 保存奖项证书时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除所有奖项证书信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有奖项证书吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用 ResumeFormHelper 清空数据
            const success = ResumeFormHelper.clearFieldData('awards', app);

            if (success) {
              // 更新页面显示
              const emptyData = ResumeFormHelper.getEmptyFieldData('awards');
              this.setData({
                awardsList: emptyData
              });

              wx.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });

              console.log('✅ 奖项证书删除成功');

              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
              console.error('❌ 奖项证书删除失败');
            }
          } catch (error) {
            console.error('❌ 删除奖项证书时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});