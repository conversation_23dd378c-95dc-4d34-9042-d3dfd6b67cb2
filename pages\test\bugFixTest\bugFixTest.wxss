/* Bug修复测试页面样式 */

.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 15rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 160rpx;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
}

.action-btn.primary {
  background-color: #52c41a;
  color: white;
}

.action-btn.secondary {
  background-color: #1890ff;
  color: white;
}

.action-btn.info {
  background-color: #722ed1;
  color: white;
}

/* 面板通用样式 */
.data-panel,
.results-panel,
.info-panel {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.panel-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-btn {
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

/* 数据显示 */
.data-content {
  padding: 20rpx 30rpx 30rpx;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.data-item:last-child {
  margin-bottom: 0;
}

.data-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.data-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  text-align: right;
}

/* 测试结果 */
.results-list {
  max-height: 600rpx;
  padding: 20rpx;
}

.result-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  border-left: 6rpx solid #52c41a;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-header {
  margin-bottom: 10rpx;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.result-message {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* 说明信息 */
.info-content {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  padding: 15rpx;
  background-color: #f6f8fa;
  border-radius: 8rpx;
}
