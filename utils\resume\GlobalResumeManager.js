/**
 * 全局简历数据管理器
 * 提供统一的简历数据访问和管理接口
 */

const { ResumeData } = require('./ResumeData.js');

/**
 * 全局简历管理器类
 */
class GlobalResumeManager {
  constructor() {
    this.currentResume = null;
    this.listeners = new Map(); // 数据变更监听器
    this.isInitialized = false;
  }

  /**
   * 初始化管理器
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      console.log('初始化全局简历管理器...');
      
      // 尝试加载当前简历
      await this.loadCurrentResume();
      
      this.isInitialized = true;
      console.log('全局简历管理器初始化完成');
    } catch (error) {
      console.error('全局简历管理器初始化失败:', error);
      // 创建默认简历
      this.currentResume = new ResumeData();
      this.isInitialized = true;
    }
  }

  /**
   * 加载当前简历
   */
  async loadCurrentResume() {
    try {
      // 首先尝试从新的存储结构加载
      const currentResumeId = wx.getStorageSync('currentResumeId');
      if (currentResumeId) {
        const resumeData = wx.getStorageSync(`resume_${currentResumeId}`);
        if (resumeData) {
          this.currentResume = ResumeData.fromObject(resumeData);
          console.log('从新存储结构加载简历成功:', currentResumeId);
          return;
        }
      }

      // 如果新结构没有数据，尝试从旧的分散存储迁移
      console.log('尝试从旧存储结构迁移数据...');
      const migratedData = this.migrateFromLegacyStorage();
      this.currentResume = new ResumeData(migratedData);
      
      // 保存迁移后的数据
      await this.saveCurrentResume();
      console.log('数据迁移完成');
      
    } catch (error) {
      console.error('加载当前简历失败:', error);
      this.currentResume = new ResumeData();
    }
  }

  /**
   * 从旧的分散存储迁移数据
   */
  migrateFromLegacyStorage() {
    console.log('开始迁移旧数据...');
    
    const legacyData = {
      basicInfo: wx.getStorageSync('basicInfo') || {},
      jobIntention: wx.getStorageSync('jobIntention') || {},
      education: wx.getStorageSync('education') || [],
      school: wx.getStorageSync('schoolList') || [],
      internship: wx.getStorageSync('internshipList') || [],
      work: wx.getStorageSync('workList') || [],
      project: wx.getStorageSync('projectList') || [],
      skills: wx.getStorageSync('skillsList') || [],
      awards: wx.getStorageSync('awardsList') || [],
      interests: wx.getStorageSync('interestsList') || [],
      evaluation: wx.getStorageSync('evaluationList') || [],
      custom1: wx.getStorageSync('customList1') || [],
      custom2: wx.getStorageSync('customList2') || [],
      custom3: wx.getStorageSync('customList3') || [],
      moduleOrders: wx.getStorageSync('moduleOrders') || {}
    };

    console.log('迁移的数据:', legacyData);
    return legacyData;
  }

  /**
   * 保存当前简历
   */
  async saveCurrentResume() {
    if (!this.currentResume) {
      console.error('没有当前简历数据可保存');
      return false;
    }

    try {
      // 更新时间戳
      this.currentResume.touch();
      
      // 生成简历ID（如果没有）
      if (!this.currentResume.id) {
        this.currentResume.id = this.generateResumeId();
      }

      // 保存到新的存储结构
      const resumeData = this.currentResume.toObject();
      wx.setStorageSync(`resume_${this.currentResume.id}`, resumeData);
      wx.setStorageSync('currentResumeId', this.currentResume.id);

      // 同时保存到旧的存储结构（兼容性）
      this.saveToLegacyStorage(resumeData);

      // 通知监听器
      this.notifyListeners('save', this.currentResume);

      console.log('简历保存成功:', this.currentResume.id);
      return true;
    } catch (error) {
      console.error('保存简历失败:', error);
      return false;
    }
  }

  /**
   * 保存到旧的存储结构（兼容性）
   */
  saveToLegacyStorage(resumeData) {
    try {
      wx.setStorageSync('basicInfo', resumeData.basicInfo);
      wx.setStorageSync('jobIntention', resumeData.jobIntention);
      wx.setStorageSync('education', resumeData.education);
      wx.setStorageSync('schoolList', resumeData.school);
      wx.setStorageSync('internshipList', resumeData.internship);
      wx.setStorageSync('workList', resumeData.work);
      wx.setStorageSync('projectList', resumeData.project);
      wx.setStorageSync('skillsList', resumeData.skills);
      wx.setStorageSync('awardsList', resumeData.awards);
      wx.setStorageSync('interestsList', resumeData.interests);
      wx.setStorageSync('evaluationList', resumeData.evaluation);
      wx.setStorageSync('customList1', resumeData.custom1);
      wx.setStorageSync('customList2', resumeData.custom2);
      wx.setStorageSync('customList3', resumeData.custom3);
      wx.setStorageSync('moduleOrders', resumeData.moduleOrders);
      
      console.log('兼容性存储保存完成');
    } catch (error) {
      console.error('兼容性存储保存失败:', error);
    }
  }

  /**
   * 获取当前简历
   */
  getCurrentResume() {
    if (!this.isInitialized) {
      console.warn('管理器未初始化，返回默认简历');
      return new ResumeData();
    }
    return this.currentResume;
  }

  /**
   * 更新当前简历的某个字段
   */
  updateField(fieldPath, value) {
    if (!this.currentResume) {
      console.error('没有当前简历数据可更新');
      return false;
    }

    try {
      // 使用点号分隔的路径更新字段
      const paths = fieldPath.split('.');
      let target = this.currentResume;
      
      for (let i = 0; i < paths.length - 1; i++) {
        if (!target[paths[i]]) {
          target[paths[i]] = {};
        }
        target = target[paths[i]];
      }
      
      target[paths[paths.length - 1]] = value;
      
      // 通知监听器
      this.notifyListeners('update', { fieldPath, value });
      
      console.log(`字段 ${fieldPath} 更新成功`);
      return true;
    } catch (error) {
      console.error(`更新字段 ${fieldPath} 失败:`, error);
      return false;
    }
  }

  /**
   * 添加数据变更监听器
   */
  addListener(key, callback) {
    this.listeners.set(key, callback);
  }

  /**
   * 移除数据变更监听器
   */
  removeListener(key) {
    this.listeners.delete(key);
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(action, data) {
    this.listeners.forEach((callback, key) => {
      try {
        callback(action, data);
      } catch (error) {
        console.error(`监听器 ${key} 执行失败:`, error);
      }
    });
  }

  /**
   * 生成简历ID
   */
  generateResumeId() {
    return 'resume_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 创建新简历
   */
  createNewResume(title = '新建简历') {
    this.currentResume = new ResumeData({
      title: title,
      id: this.generateResumeId()
    });
    
    this.notifyListeners('create', this.currentResume);
    console.log('创建新简历:', this.currentResume.id);
    return this.currentResume;
  }

  /**
   * 验证当前简历
   */
  validateCurrentResume() {
    if (!this.currentResume) {
      return ['没有简历数据'];
    }
    return this.currentResume.validate();
  }

  /**
   * 获取简历的JSON表示（用于传输）
   */
  getCurrentResumeJSON() {
    if (!this.currentResume) {
      return '{}';
    }
    return this.currentResume.toJSON();
  }

  /**
   * 重置管理器
   */
  reset() {
    this.currentResume = null;
    this.listeners.clear();
    this.isInitialized = false;
  }
}

// 创建全局单例
const globalResumeManager = new GlobalResumeManager();

module.exports = globalResumeManager;
