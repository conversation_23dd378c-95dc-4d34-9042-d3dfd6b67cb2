<view class="container">
  <!-- 在校经历列表 -->
  <view class="schoolList" wx:if="{{schoolFormData.length > 0}}">
    <view class="schoolItem" 
          wx:for="{{schoolFormData}}" 
          wx:key="index"
          bindlongpress="handleLongPress"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd"
          data-index="{{index}}"
          style="{{currentIndex === index ? 'background: #f5f5f5; transform: scale(1.02);' : ''}}">
      <view class="itemContent" bindtap="editSchool" data-index="{{index}}">
        <view class="mainInfo">
          <text class="title">{{item.title}}</text>
          <text class="role">{{item.role}}</text>
        </view>
        <text class="dateText">{{item.startDate}} - {{item.endDate}}</text>
      </view>
      <view class="actionButtons">
        <view class="editBtn" catchtap="editSchool" data-index="{{index}}">
          <text class="editIcon">✎</text>
        </view>
        <view class="deleteBtn" catchtap="deleteSchool" data-index="{{index}}">
          <text class="deleteIcon">×</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="emptyState" wx:else>
    <text class="emptyText">暂无在校经历，点击下方按钮添加</text>
  </view>

  <!-- 添加按钮 -->
  <view class="addBtn" bindtap="addSchool">
    + 添加更多在校经历
  </view>
</view> 