# API重构和用户状态管理调整总结

## 调整概述

本次调整主要完成了以下几个方面的优化：
1. 去除用户中心的登录页面和相关用户触发的登录代码
2. 实现userToken和userId的全局使用
3. 将服务器交互请求函数独立到专门的API模块
4. 在所有服务器请求中统一添加userId和Bearer token

## 主要变更

### 1. 创建统一的API请求工具 (`utils/api/request.js`)

**核心功能**:
- 统一的请求封装，自动处理loading、错误提示
- 自动添加认证头（Authorization: Bearer token, X-User-Id: userId）
- 支持相对路径和绝对路径URL
- 统一的错误处理和业务逻辑处理
- 提供GET、POST、PUT、DELETE等便捷方法

**主要特性**:
```javascript
// 自动添加认证头
const authHeader = {
  'Authorization': `Bearer ${userToken}`,
  'X-User-Id': userId
};

// 统一的请求方法
request.get('/user/info')    // GET请求
request.post('/user/login', data)  // POST请求
```

### 2. 创建专门的API模块

**用户API模块** (`utils/api/userApi.js`):
- `login()` - 用户登录
- `validateToken()` - 验证token有效性
- `getUserInfo()` - 获取用户信息
- `recordUserAction()` - 记录用户行为

**反馈API模块** (`utils/api/feedbackApi.js`):
- `submitFeedback()` - 提交用户反馈
- `getFeedbackList()` - 获取反馈列表

### 3. 创建全局用户状态管理工具 (`utils/user/userState.js`)

**核心功能**:
- 统一管理用户Token、用户ID、会员信息
- 自动同步本地存储和全局状态
- 提供便捷的状态检查方法

**主要方法**:
```javascript
userState.getUserToken()      // 获取用户Token
userState.getUserId()         // 获取用户ID
userState.isLoggedIn()        // 检查是否已登录
userState.isMember()          // 检查是否为会员
userState.setUserLoginInfo()  // 设置登录信息
userState.clearUserLoginInfo() // 清除登录信息
```

### 4. 页面代码重构

**自动登录工具类** (`utils/user/autoLogin.js`):
- 使用新的userApi模块替代直接的wx.request
- 简化了代码逻辑，提高了可维护性

**登录页面** (`pages/user/login/login.js`):
- 使用userApi.login()替代直接请求
- 移除了重复的请求处理代码

**反馈页面** (`pages/feedback/feedback.js`):
- 使用feedbackApi.submitFeedback()替代直接请求
- 自动处理认证和错误

**用户中心页面** (`pages/user/center/center.js`):
- 移除了手动登录相关代码
- 简化了页面逻辑

### 5. 去除用户中心登录功能

**移除的功能**:
- `goToLogin()` 方法
- 登录按钮的条件显示
- 手动触发登录的相关代码

**保留的功能**:
- 退出登录功能
- 会员状态显示
- 基本的用户信息展示

## 技术优势

### 1. 代码复用性
- 所有页面共享同一套API请求逻辑
- 减少了重复代码，提高了开发效率
- 统一的错误处理和loading管理

### 2. 维护性
- API接口变更时只需修改API模块
- 认证逻辑统一管理，易于调试
- 清晰的模块分离，职责明确

### 3. 安全性
- 统一的认证头管理
- 自动处理token失效情况
- 防止认证信息泄露

### 4. 用户体验
- 统一的loading和错误提示
- 自动重试机制
- 更好的错误信息展示

## 请求头格式

所有需要认证的API请求都会自动添加以下请求头：

```javascript
{
  'Content-Type': 'application/json',
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  'X-User-Id': 'user_12345'
}
```

## 使用示例

### 1. 在页面中使用API

```javascript
// 旧方式（不推荐）
wx.request({
  url: 'https://api.example.com/user/info',
  method: 'GET',
  header: {
    'Authorization': `Bearer ${wx.getStorageSync('userToken')}`,
    'X-User-Id': wx.getStorageSync('userId')
  },
  success: (res) => {
    // 处理响应
  }
});

// 新方式（推荐）
const userApi = require('../../utils/api/userApi');
userApi.getUserInfo()
  .then((data) => {
    // 处理数据
  })
  .catch((error) => {
    // 错误已自动处理
  });
```

### 2. 检查用户状态

```javascript
// 旧方式
const token = wx.getStorageSync('userToken');
const userId = wx.getStorageSync('userId');
const isLoggedIn = !!(token && userId);

// 新方式
const userState = require('../../utils/user/userState');
const isLoggedIn = userState.isLoggedIn();
const isMember = userState.isMember();
```

## 服务器端适配

### 1. 请求头处理
服务器需要处理以下请求头：
- `Authorization: Bearer <token>` - 用户认证token
- `X-User-Id: <userId>` - 用户ID（可选，用于日志和调试）

### 2. 接口路径
建议使用RESTful风格的API路径：
- `POST /user/login` - 用户登录
- `GET /user/validate` - 验证token
- `GET /user/info` - 获取用户信息
- `POST /user/action` - 记录用户行为
- `POST /feedback/submit` - 提交反馈

## 迁移指南

### 1. 现有页面迁移
1. 引入对应的API模块
2. 替换wx.request调用为API方法调用
3. 移除手动的认证头处理
4. 简化错误处理逻辑

### 2. 新页面开发
1. 直接使用API模块
2. 使用userState检查用户状态
3. 遵循统一的错误处理模式

## 注意事项

### 1. 兼容性
- 保持了原有的全局数据结构
- 现有的用户状态检查逻辑仍然有效
- 渐进式迁移，不影响现有功能

### 2. 性能
- 减少了重复的网络请求
- 优化了用户状态同步逻辑
- 统一的loading管理避免重复显示

### 3. 调试
- 统一的日志输出格式
- 更好的错误信息
- 便于问题定位和排查

这次重构大大提升了代码的可维护性和开发效率，为后续功能开发奠定了良好的基础。
