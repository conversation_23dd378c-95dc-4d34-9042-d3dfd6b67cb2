.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.schoolList {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.schoolItem {
  position: relative;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.schoolItem:last-child {
  border-bottom: none;
}

.itemContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-right: 100rpx;
}

.mainInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.role {
  font-size: 28rpx;
  color: #666;
}

.dateText {
  font-size: 26rpx;
  color: #999;
  min-width: 180rpx;
  text-align: right;
}

.actionButtons {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.editBtn, .deleteBtn {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.editBtn:active, .deleteBtn:active {
  opacity: 0.7;
}

.editIcon {
  color: #4B8BF5;
  font-size: 32rpx;
}

.deleteIcon {
  color: #999;
  font-size: 36rpx;
}

.emptyState {
  background: #fff;
  border-radius: 16rpx;
  text-align: center;
  padding: 60rpx 0;
  margin: 20rpx 0;
}

.emptyText {
  font-size: 28rpx;
  color: #999;
}

.addBtn {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  background: #4B8BF5;
  color: #fff;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(75, 139, 245, 0.2);
}

.addBtn:active {
  transform: scale(0.98);
  opacity: 0.9;
} 