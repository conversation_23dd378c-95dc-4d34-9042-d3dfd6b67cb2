const ResumeFormHelper = require('../../../utils/resume/ResumeFormHelper.js');
const app = getApp();

Page({
  data: {
    skillsList: null  // 将在 onLoad 中初始化
  },

  onLoad() {
    console.log('=== 技能特长页面加载 ===');
    this.loadSkillsData();
  },

  /**
   * 从全局管理器加载技能特长数据
   */
  loadSkillsData() {
    try {
      const skillsData = ResumeFormHelper.loadFieldData('skills', app);

      // skillsData 现在是一个包含16个元素的数组（包括空字符串）
      this.setData({
        skillsList: skillsData
      });

      console.log('✅ 技能特长数据加载成功:', skillsData);
    } catch (error) {
      console.error('❌ 加载技能特长数据失败:', error);
      // 出错时使用空数据
      const emptyData = ResumeFormHelper.getEmptyFieldData('skills');
      this.setData({
        skillsList: emptyData
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;

    const skillsList = [...this.data.skillsList];
    skillsList[index] = value;

    this.setData({
      skillsList: skillsList
    });
  },

  /**
   * 保存技能特长信息
   */
  saveInfo() {
    try {
      // 过滤掉空字符串，并确保每个项都是字符串
      const skillsList = this.data.skillsList
        .map(item => {
          if (typeof item === 'object' && item !== null) {
            return item.content || '';
          }
          return item;
        })
        .filter(item => typeof item === 'string' && item.trim() !== '');

      // 使用 ResumeFormHelper 统一保存
      const success = ResumeFormHelper.saveFieldData('skills', skillsList, app);

      if (success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 1500
        });

        console.log('✅ 技能特长保存成功:', skillsList);

        setTimeout(() => {
          wx.navigateBack();
        }, 500);
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('❌ 技能特长保存失败');
      }
    } catch (error) {
      console.error('❌ 保存技能特长时发生错误:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除所有技能特长信息
   */
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有技能特长吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            // 使用 ResumeFormHelper 清空数据
            const success = ResumeFormHelper.clearFieldData('skills', app);

            if (success) {
              // 更新页面显示
              const emptyData = ResumeFormHelper.getEmptyFieldData('skills');
              this.setData({
                skillsList: emptyData
              });

              wx.showToast({
                title: '已删除',
                icon: 'success',
                duration: 1500
              });

              console.log('✅ 技能特长删除成功');

              setTimeout(() => {
                wx.navigateBack();
              }, 500);
            } else {
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
              console.error('❌ 技能特长删除失败');
            }
          } catch (error) {
            console.error('❌ 删除技能特长时发生错误:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  }
});