<!-- pages/feedback/feedback.wxml -->
<custom-nav-bar title="意见反馈" show-back="{{true}}" show-share="{{true}}" show-feedback="{{false}}"></custom-nav-bar>

<view class="container">
  <!-- 反馈类型选择 -->
  <view class="section">
    <view class="section-title">反馈类型</view>
    <view class="type-list">
      <view 
        class="type-item {{feedbackType === item.value ? 'active' : ''}}" 
        wx:for="{{typeOptions}}" 
        wx:key="value"
        bindtap="onTypeChange"
        data-type="{{item.value}}"
      >
        <text class="type-icon">{{item.icon}}</text>
        <text class="type-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 反馈内容 -->
  <view class="section">
    <view class="section-title">反馈内容 <text class="required">*</text></view>
    <textarea 
      class="feedback-textarea"
      placeholder="请详细描述您的问题或建议，我们会认真对待每一条反馈..."
      value="{{feedbackContent}}"
      bindinput="onContentInput"
      maxlength="500"
      show-confirm-bar="{{false}}"
      auto-height
    ></textarea>
    <view class="char-count">{{feedbackContent.length}}/500</view>
  </view>

  <!-- 联系方式 -->
  <view class="section">
    <view class="section-title">联系方式 <text class="optional">(选填)</text></view>
    <input 
      class="contact-input"
      placeholder="微信号、QQ号或邮箱，方便我们与您联系"
      value="{{contactInfo}}"
      bindinput="onContactInput"
      maxlength="50"
    />
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{feedbackContent.length >= 10 ? 'enabled' : 'disabled'}}"
      bindtap="submitFeedback"
      disabled="{{isSubmitting || feedbackContent.length < 10}}"
      loading="{{isSubmitting}}"
    >
      {{isSubmitting ? '提交中...' : '提交反馈'}}
    </button>
  </view>

  <!-- 说明文字 -->
  <view class="tips">
    <text class="tips-text">• 我们会认真对待每一条反馈</text>
    <text class="tips-text">• 您的建议将帮助我们改进产品</text>
    <text class="tips-text">• 如需回复，请留下联系方式</text>
  </view>
</view>
