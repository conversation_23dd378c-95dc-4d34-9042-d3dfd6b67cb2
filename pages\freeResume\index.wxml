<!-- 免费模板页面 -->
<view class="free-resume-page">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="header-content">
      <text class="title">简历制作个人简历模板</text>
      <view class="subtitle-container">
        <view class="megaphone-icon">📢</view>
        <view class="subtitle-text">
          <text class="highlight">以下所有模板为word模板</text>
          <text class="normal">不支持手机编辑</text>
          <text class="highlight">请发送到电脑端word编辑</text>
        </view>
      </view>
      <view class="action-text">在线简历制作请从在线入口进入</view>
    </view>
  </view>

  <!-- 模板列表 -->
  <view class="template-list-container">
    <scroll-view
      class="template-scroll"
      scroll-y
      refresher-enabled="{{true}}"
      refresher-triggered="{{isRefreshing}}"
      bindrefresherrefresh="onRefresh"
      bindscrolltolower="onLoadMore"
    >
      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{isLoading && templates.length === 0}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载模板...</text>
      </view>

      <!-- 模板网格 - 两列布局 -->
      <view class="template-grid" wx:if="{{templates.length > 0}}">
        <view
          class="template-item"
          wx:for="{{templates}}"
          wx:key="id"
          bindtap="onTemplateClick"
          data-template="{{item}}"
        >
          <view class="template-image-container">
            <image
              class="template-image"
              src="{{item.thumb_url}}"
              mode="aspectFit"
              lazy-load="{{true}}"
              bindload="onImageLoad"
              binderror="onImageError"
              data-index="{{index}}"
            ></image>
            <view class="image-overlay" wx:if="{{item.isLoading}}">
              <view class="overlay-spinner"></view>
            </view>
            <view class="image-error-overlay" wx:if="{{item.imageError}}">
              <text class="error-text">图片加载失败</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!isLoading && templates.length === 0}}">
        <view class="empty-icon">📄</view>
        <text class="empty-text">暂无模板数据</text>
        <button class="retry-btn" bindtap="onRetry">重新加载</button>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore && templates.length > 0}}">
        <view class="load-more-spinner" wx:if="{{isLoadingMore}}"></view>
        <text class="load-more-text">{{isLoadingMore ? '加载中...' : '上拉加载更多'}}</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && templates.length > 0}}">
        <text class="no-more-text">已显示全部模板</text>
      </view>
    </scroll-view>
  </view>

  <!-- 错误提示 -->
  <view class="error-toast" wx:if="{{errorMessage}}">
    <text class="error-text">{{errorMessage}}</text>
    <button class="error-close" bindtap="clearError">×</button>
  </view>

  <!-- 下载链接浮窗 -->
  <download-modal
    visible="{{showDownloadModal}}"
    templateData="{{selectedTemplate}}"
    downloadData="{{downloadData}}"
    isLoading="{{isLoadingDownload}}"
    bind:close="onCloseDownloadModal"
    bind:copy="onCopySuccess"
    bind:copyFail="onCopyFail"
  ></download-modal>
</view>
